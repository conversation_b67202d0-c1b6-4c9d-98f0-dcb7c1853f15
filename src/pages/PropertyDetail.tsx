
import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import PageTransition from '../components/layout/PageTransition';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { mapPropertyToCardProperty } from '@/utils/propertyUtils';
import { useAuth } from '@/contexts/AuthContext';
import { Property as CardProperty, CollectionWithBudget } from '@/components/properties/PropertyCard';
import { usePendingItems } from '@/hooks/usePendingItems';
import BookingCalendar from '@/components/properties/calendar';
import PropertyDetailHeader from '@/components/properties/PropertyDetailHeader';
import PropertySummary from '@/components/properties/PropertySummary';
import PropertyOverviewTab from '@/components/properties/PropertyOverviewTab';
import PropertyPendingItems from '@/components/properties/PropertyPendingItems';
import CollectionsTab from '@/components/properties/CollectionsTab';
import PropertyEditForm from '@/components/properties/PropertyEditForm';
import usePropertyDetail from '@/hooks/usePropertyDetail';
import PropertyCollectionsManager from '@/components/properties/PropertyCollectionsManager';
import PropertyDocumentsTab from '@/components/properties/PropertyDocumentsTab';
import usePropertyUpdate from '@/hooks/usePropertyUpdate';
import PropertyActions from '@/components/properties/PropertyActions';

const PropertyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const userId = authState.user?.id;

  const { property, loading, error, setProperty, fetchProperty } = usePropertyDetail(id, userId);
  const { pendingItems, loading: pendingLoading } = usePendingItems(id, userId);
  const { handleUpdateProperty } = usePropertyUpdate();

  const [isEditMode, setIsEditMode] = useState(false);
  const [editProperty, setEditProperty] = useState<CardProperty | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const handleEdit = () => {
    setIsEditMode(true);
    if (property) {
      setEditProperty(mapPropertyToCardProperty(property));
    }
  };

  const handleSaveEdit = () => {
    if (property && editProperty) {
      handleUpdateProperty(id, property, editProperty as any).then(data => {
        if (data) {
          setProperty(prev => prev ? {
            ...prev,
            ...data,
            collections: data.collections || prev.collections
          } : null);
        }
        setIsEditMode(false);
      });
    }
  };

  const handleCancelEdit = () => {
    setIsEditMode(false);
  };

  const handleManageCollections = () => {
    setActiveTab('collections');
  };

  const handleCollectionsChange = (updatedCollections: CollectionWithBudget[]) => {
    if (!property) return;

    // Create a property update with just the collections
    const propertyUpdate = {
      id: property.id,
      user_id: userId,
      collections: updatedCollections
    };

    handleUpdateProperty(property.id, property, propertyUpdate as any).then(data => {
      if (data) {
        setProperty(prev => prev ? {
          ...prev,
          collections: data.collections || prev.collections
        } : null);
      }
    });
  }

  if (loading) {
    return (
      <PageTransition>
        <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[50vh]">
          <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
          <p className="text-muted-foreground">Loading property details...</p>
        </div>
      </PageTransition>
    );
  }

  if (error || !property) {
    return (
      <PageTransition>
        <div className="container mx-auto px-4 py-8">
          <div className="glass rounded-xl p-8 text-center">
            <h2 className="text-2xl font-bold mb-4 text-destructive">Error</h2>
            <p className="text-muted-foreground mb-6">{error || 'Property not found'}</p>
            <Button onClick={() => navigate('/properties')}>
              Return to Properties
            </Button>
          </div>
        </div>
      </PageTransition>
    );
  }

  if (isEditMode) {
    return (
      <PageTransition>
        <PropertyEditForm
          property={property}
          editProperty={editProperty}
          setEditProperty={setEditProperty}
          onSave={handleSaveEdit}
          onCancel={handleCancelEdit}
          userId={userId}
        />
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="container mx-auto px-4 py-8 pb-32">
        <PropertyDetailHeader
          property={property}
          onEdit={handleEdit}
        />

        <PropertySummary property={property} />

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="collections">Collections</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="pending">Pending Items</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <PropertyOverviewTab
              property={property}
              userId={userId}
              onSyncComplete={fetchProperty}
              onManageCollections={handleManageCollections}
            />
          </TabsContent>

          <TabsContent value="collections">
            <PropertyCollectionsManager
              propertyId={property.id}
              collections={property.collections || []}
              onCollectionsChange={handleCollectionsChange}
            />
          </TabsContent>

          <TabsContent value="documents">
            <PropertyDocumentsTab
              propertyId={property.id}
              isPropertyOwner={property.user_id === userId}
            />
          </TabsContent>

          <TabsContent value="pending">
            <PropertyPendingItems
              pendingItems={pendingItems}
              propertyId={property.id}
              loading={pendingLoading}
            />
          </TabsContent>
        </Tabs>

        {property && <BookingCalendar propertyId={property.id} />}
      </div>
    </PageTransition>
  );
};

export default PropertyDetail;
