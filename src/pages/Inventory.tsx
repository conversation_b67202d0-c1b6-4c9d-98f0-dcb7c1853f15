import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Property } from '@/types/inventory';
import { toast } from 'sonner';
import { ensureStorageBuckets } from '@/utils/setupStorage';
import { forceCloseDialog } from '@/utils/forceCloseDialog';
import { createStorageBuckets } from '@/utils/createStorageBuckets';
import { useInventoryQueryV2 } from '@/hooks/useInventoryQueryV2';
import { useInventoryFilters } from '@/hooks/useInventoryFilters';
import { useInventoryOperations } from '@/hooks/useInventoryOperations';
import { usePurchaseOrders } from '@/hooks/usePurchaseOrders';
import PageTransition from '@/components/layout/PageTransition';
import InventoryHeader from '@/components/inventory/InventoryHeader';
import InventoryFilterBar from '@/components/inventory/InventoryFilterBar';
import InventoryContent from '@/components/inventory/InventoryContent';
import InventoryDialog from '@/components/inventory/InventoryDialog';
import CreatePurchaseOrderDialog from '@/components/inventory/CreatePurchaseOrderDialog';
import BulkImportDialog from '@/components/inventory/BulkImportDialog';
import InventorySummary from '@/components/inventory/InventorySummary';
import { Package } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AmazonSearch from '@/components/inventory/AmazonSearch';

const Inventory = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<string>('inventory');
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [extensionImportData, setExtensionImportData] = useState<{ items: any[], isExtensionImport: boolean } | null>(null);

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(prev => !prev);
  };

  // Get inventory operations
  const {
    selectedItem,
    itemDialog,
    purchaseOrderDialog,
    bulkImportDialog,
    handleItemClick,
    handleAddItem,
    handleCreatePurchaseOrder,
    handleBulkImport,
    handleSaveItem,
    handleDeleteItems,
    handleBulkSaveItems,
  } = useInventoryOperations();

  // Check if storage buckets exist and set up global variables
  useEffect(() => {
    const setupStorage = async () => {
      try {
        // Set default values regardless of storage availability
        window.INVENTORY_BUCKET_NAME = 'inventory';
        window.INVENTORY_FOLDER_PATH = 'inventory-images';

        // First check if buckets exist
        const exists = await ensureStorageBuckets();

        if (!exists) {
          console.warn('Storage buckets could not be verified. Will use edge functions for uploads.');

          // Try to check bucket access without creating them
          const hasAccess = await createStorageBuckets();

          if (hasAccess) {
            console.log('Successfully verified storage bucket access');
          } else {
            console.warn('Limited storage bucket access. Will use edge functions or fallback to local images.');
          }
        } else {
          console.log('Storage buckets verified and ready for use');
        }
      } catch (error) {
        console.error('Failed to set up storage buckets:', error);
        console.warn('Will use edge functions or fallback to local images for uploads.');
      }
    };

    setupStorage();
  }, []);

  // Listen for import data messages from the content script
  useEffect(() => {
    console.log('[Inventory] Setting up message listener useEffect.'); // Add log here
    const handleMessage = (event: MessageEvent) => {
      // Log ALL messages received by the window to debug
      console.log('[Inventory] Raw window message received:', {
        origin: event.origin,
        sourceCheck: event.source === window ? 'same-window' : 'different-window/iframe',
        dataType: typeof event.data,
        data: event.data
      });

      // Check 1: Origin must match
      if (event.origin !== window.location.origin) {
        // console.log('[Inventory] Ignoring message: Origin mismatch.'); // Optional log
        return;
      }

      // Check 2: Data must be an object with the correct type property
      if (typeof event.data !== 'object' || event.data === null || event.data.type !== 'STAYFU_IMPORT_FROM_EXTENSION') {
         // console.log('[Inventory] Ignoring message: Invalid type or data format.'); // Optional log
        return;
      }

      try {
        // If all checks pass so far, declare importData
        const importData = event.data.data;
        console.log('[Inventory] Received valid STAYFU_IMPORT_FROM_EXTENSION message:', event.data); // Log only once after validation

        // Check 3: Ensure the actual data payload exists and is an array
        if (!importData || !Array.isArray(importData)) {
           console.warn('[Inventory] Received import message but event.data.data is missing or not an array.');
           toast.error('Received invalid import data structure from extension.');
           return;
        }

        // Proceed with valid data
        if (importData.length > 0) {
          console.log('[Inventory] Setting extensionImportData state and opening dialog.');
          // Make a deep copy to ensure we don't have any reference issues
          // Also ensure each item has the required properties with correct types
          const safeItems = importData.map(item => ({
            name: String(item.name || 'Unnamed Product'),
            propertyId: String(item.propertyId || ''),
            propertyName: String(item.propertyName || ''),
            collection: String(item.collection || ''),
            quantity: Number(item.quantity || 1),
            minQuantity: Number(item.minQuantity || 0),
            price: Number(item.price || 0),
            amazonUrl: String(item.amazonUrl || ''),
            imageUrl: String(item.imageUrl || '')
          }));

          setExtensionImportData({
            items: safeItems,
            isExtensionImport: true
          });
          bulkImportDialog.onOpen();
        } else {
          console.warn('[Inventory] Received import message but data was invalid or empty.');
          toast.error('Received invalid import data from extension.');
        }
      } catch (error) {
        console.error('[Inventory] Error processing import data:', error);
        toast.error('Error processing import data. Please try again.');
      }
    };

    console.log('[Inventory] Adding window message listener for extension imports.');
    window.addEventListener('message', handleMessage);

    // Cleanup listener on component unmount
    return () => {
      console.log('[Inventory] Removing window message listener.');
      window.removeEventListener('message', handleMessage);
    };
  }, []); // Empty dependency array ensures listener is added/removed only on mount/unmount
  // Fetch inventory data with retry capability
  const {
    inventoryItems,
    loading: isLoading,
    error,
    fetchInventory: retryFetch
  } = useInventoryQueryV2();

  // Fetch properties with retry mechanism
  const { data: propertiesData = [], refetch: refetchProperties, isLoading: isLoadingProperties } = useQuery({
    queryKey: ['properties'],
    queryFn: async () => {
      console.log('[Inventory] Fetching properties');

      const { data, error } = await supabase
        .from('properties')
        .select('id, name')
        .order('name');

      if (error) {
        console.error('[Inventory] Error fetching properties:', error);
        throw error;
      }

      // If we got an empty array but we expect properties, throw an error to trigger retry
      if (Array.isArray(data) && data.length === 0) {
        console.warn('[Inventory] Received empty properties array, might be a connection issue');
        // Only throw if we're not in initial load
        if (propertiesData.length > 0) {
          throw new Error('Received empty properties array when we had properties before');
        }
      }

      console.log(`[Inventory] Successfully loaded ${data?.length || 0} properties`);
      return data as Property[];
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    networkMode: 'always'
  });

  // Filter out duplicate properties by name (without user info in the name)
  const properties = React.useMemo(() => {
    console.log('[Inventory] Processing properties data:', propertiesData);

    // Create a map to store unique properties by name
    const uniquePropertiesByName = new Map<string, Property>();

    // Process properties to keep only one entry per property name
    // We'll clean the property name by removing user info in parentheses
    for (const property of propertiesData) {
      // Extract the base property name without the user info in parentheses
      const baseName = property.name.replace(/\s*\([^)]*\)\s*$/, '').trim();
      const key = baseName.toLowerCase();

      if (!uniquePropertiesByName.has(key)) {
        // Store with the original name but keyed by the base name
        uniquePropertiesByName.set(key, {
          ...property,
          name: baseName // Use the cleaned name without user info
        });
      }
    }

    // Convert the map values to an array
    const result = Array.from(uniquePropertiesByName.values());

    console.log('[Inventory] Filtered unique properties:', result);
    return result;
  }, [propertiesData]);

  // Format inventory data for use in components
  const formattedItems = useMemo(() => {
    if (!inventoryItems) return [];

    console.log('[Inventory] Processing inventory items with properties:', {
      itemsCount: inventoryItems.length,
      propertiesCount: properties.length,
      sampleItem: inventoryItems.length > 0 ? inventoryItems[0] : null
    });

    return inventoryItems.map((item: any) => {
      // Try to find the property in our properties array to get the correct name
      const property = properties.find(p => p.id === item.propertyId);
      const propertyName = property ? property.name : (item.propertyName || 'Unknown Property');

      // Log any items with missing property information
      if (!item.propertyId) {
        console.warn('[Inventory] Item missing propertyId:', item.name);
      }

      // The items are already formatted by the hook, just ensure property names are correct
      return {
        ...item,
        propertyName: propertyName
      };
    });
  }, [inventoryItems, properties]);

  // Filtering and search logic - use formattedItems which includes property names
  const {
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    filteredItems,
    collections
  } = useInventoryFilters(formattedItems || []);

  // Send property and collection data to the extension when available
  useEffect(() => {
    const extensionId = import.meta.env.VITE_CHROME_EXTENSION_ID;

    // Ensure properties and collections have loaded and extension API is available
    // Use propertiesData to check if the initial fetch is done, even if it resulted in 0 properties
    if (propertiesData && collections && extensionId && typeof chrome !== 'undefined' && chrome.runtime?.sendMessage) {
      console.log('[Inventory] Sending property and collection data to extension:', { properties, collections });
      try {
        chrome.runtime.sendMessage(
          extensionId,
          {
            action: 'updatePropertyData',
            propertyData: {
              properties: properties, // Send the processed unique properties
              collections: collections
            }
          },
          (response) => {
            if (chrome.runtime.lastError) {
              console.error('[Inventory] Error sending data to extension:', chrome.runtime.lastError.message);
              // Optional: Show a subtle toast or log, but maybe not a blocking error
              // toast.error(`Failed to sync properties with extension: ${chrome.runtime.lastError.message}`);
            } else if (response && response.success) {
              console.log('[Inventory] Extension confirmed receipt of property data.');
            } else {
              console.warn('[Inventory] Extension did not successfully process property data:', response?.error);
              // Optional: toast.warning(`Extension sync issue: ${response?.error || 'Unknown error'}`);
            }
          }
        );
      } catch (error) {
        console.error('[Inventory] Failed to initiate message sending to extension:', error);
        // Optional: toast.error('Could not communicate with the extension.');
      }
    } else {
       // Log why data wasn't sent if needed for debugging
       if (!propertiesData || !collections) {
         console.log('[Inventory] Waiting for properties/collections to load before sending to extension.');
       }
       if (!extensionId) {
         console.warn('[Inventory] Extension ID not configured, cannot send data.');
       }
       if (typeof chrome === 'undefined' || !chrome.runtime?.sendMessage) {
         console.warn('[Inventory] Chrome runtime not available, cannot send data.');
       }
    }
  }, [properties, collections, propertiesData]); // Depend on properties, collections, and the raw propertiesData

  // View mode is handled directly by the setViewMode function

  // Import the usePurchaseOrders hook
  const { createPurchaseOrder } = usePurchaseOrders();

  // Handle purchase order creation
  const handleSavePurchaseOrder = (orderData: any) => {
    console.log('[Inventory] Purchase order created:', orderData);

    // Validate the order data
    if (!orderData.propertyId) {
      console.error('[Inventory] Property ID is missing in order data');

      // Try to find a property ID in the items
      if (orderData.items && orderData.items.length > 0) {
        // Look for a property ID in the items
        const firstItem = orderData.items[0];
        const itemPropertyId = firstItem.property_id || firstItem.propertyId;

        if (itemPropertyId) {
          console.log(`[Inventory] Found property ID in first item: ${itemPropertyId}`);
          orderData.propertyId = itemPropertyId;
        } else if (properties.length > 0) {
          // If we still don't have a property ID, use the first available property
          orderData.propertyId = properties[0].id;
          console.log(`[Inventory] Using first available property: ${orderData.propertyId}`);
          toast.info(`Using property: ${properties[0].name}`);
        } else {
          toast.error('No valid property found. Please select a property and try again.');
          return;
        }
      } else if (properties.length > 0) {
        // If we don't have items, use the first available property
        orderData.propertyId = properties[0].id;
        console.log(`[Inventory] Using first available property: ${orderData.propertyId}`);
        toast.info(`Using property: ${properties[0].name}`);
      } else {
        toast.error('No valid property found. Please select a property and try again.');
        return;
      }
    }

    if (!orderData.items || orderData.items.length === 0) {
      console.error('[Inventory] No items in order data');
      toast.error('No items selected. Please select at least one item.');
      return;
    }

    // Format the data for the API
    const dbOrderData = {
      property_id: orderData.propertyId,
      status: 'pending',
      total_price: orderData.totalPrice,
      notes: orderData.notes || '',
      is_archived: false
    };

    // Group items by property
    const itemsByProperty = orderData.items.reduce((acc, item) => {
      // Use the item's original property ID if available, otherwise use the order's property ID
      const propertyId = item.propertyId || orderData.propertyId;

      if (!acc[propertyId]) {
        acc[propertyId] = [];
      }

      acc[propertyId].push(item);
      return acc;
    }, {} as Record<string, any[]>);

    console.log('[Inventory] Items grouped by property:', itemsByProperty);

    // If we have items from multiple properties, create separate orders
    const propertyIds = Object.keys(itemsByProperty);

    if (propertyIds.length > 1) {
      console.log('[Inventory] Creating separate orders for multiple properties');

      // Create a separate order for each property
      let successCount = 0;
      let errorCount = 0;
      let totalProperties = propertyIds.length;

      // Create a variable to track if the operation completed normally
      let operationCompleted = false;

      // Add a timeout to ensure the dialog closes even if there are issues
      const safetyTimeout = setTimeout(() => {
        console.log('[Inventory] Safety timeout triggered - closing dialog');
        if (!operationCompleted) {
          purchaseOrderDialog.onClose();
          toast.warning('Purchase orders may have been created, but the process took longer than expected. Please check the Purchase Orders page.');
          navigate('/purchase-orders');
        }
      }, 10000); // 10 seconds timeout

      // Function to check if we're done processing all properties
      const checkCompletion = () => {
        if (successCount + errorCount === totalProperties) {
          operationCompleted = true;
          clearTimeout(safetyTimeout);
          purchaseOrderDialog.onClose();

          if (errorCount === 0) {
            toast.success(`Created ${successCount} purchase orders successfully`);
          } else if (successCount === 0) {
            toast.error(`Failed to create any purchase orders. Please try again.`);
          } else {
            toast.warning(`Created ${successCount} purchase orders with ${errorCount} failures`);
          }

          navigate('/purchase-orders');
        }
      };

      // Process each property's items
      propertyIds.forEach(propertyId => {
        const items = itemsByProperty[propertyId];
        const propertyName = items[0]?.propertyName || 'Unknown Property';

        console.log(`[Inventory] Processing items for property: ${propertyId} (${propertyName})`);

        // Format the items for this property
        const formattedItems = items.map(item => {
          // Ensure we have a valid item name
          const itemName = item.item_name || item.name || 'Unnamed Item';

          // Ensure we have a valid quantity
          const quantity = item.quantity || item.orderQuantity || 1;

          // Create the item object with all required fields
          return {
            inventory_item_id: item.inventory_item_id || item.id,
            item_name: itemName,
            quantity: quantity,
            price: item.price || 0,
            amazon_url: item.amazon_url || item.amazonUrl || '',
            walmart_url: item.walmart_url || item.walmartUrl || ''
          };
        });

        // Calculate total price for this property's items
        const totalPrice = formattedItems.reduce((sum, item) =>
          sum + (item.price || 0) * item.quantity, 0);

        // Create order data for this property
        const propertyOrderData = {
          property_id: propertyId,
          status: 'pending',
          total_price: totalPrice,
          notes: orderData.notes || '',
          is_archived: false
        };

        console.log(`[Inventory] Creating order for property ${propertyId} with ${formattedItems.length} items`);

        // Create the purchase order for this property
        createPurchaseOrder.mutate({
          orderData: propertyOrderData,
          items: formattedItems
        }, {
          onSuccess: () => {
            console.log(`[Inventory] Successfully created order for property: ${propertyId}`);
            successCount++;
            checkCompletion();
          },
          onError: (error) => {
            console.error(`[Inventory] Error creating order for property ${propertyId}:`, error);
            toast.error(`Failed to create order for ${propertyName}: ${error.message}`);
            errorCount++;
            checkCompletion();
          }
        });
      });

      // Return early since we're handling the orders separately
      return;
    }

    // If we only have one property, proceed with the original logic
    // Format the items for the API
    const orderItems = orderData.items.map(item => {
      // Ensure we have a valid item name
      const itemName = item.item_name || item.name || 'Unnamed Item';

      // Ensure we have a valid quantity
      const quantity = item.quantity || item.orderQuantity || 1;

      // Create the item object with all required fields
      return {
        inventory_item_id: item.inventory_item_id || item.id,
        item_name: itemName,
        quantity: quantity,
        price: item.price || 0,
        amazon_url: item.amazon_url || item.amazonUrl || '',
        walmart_url: item.walmart_url || item.walmartUrl || ''
      };
    });

    console.log('[Inventory] Creating purchase order with:', {
      orderData: dbOrderData,
      items: orderItems
    });

    // Add additional validation
    if (!dbOrderData.property_id || dbOrderData.property_id === 'all') {
      console.error('[Inventory] Invalid property ID:', dbOrderData.property_id);
      toast.error('Invalid property selected. Please select a specific property.');
      return;
    }

    // Validate that all items have required fields
    const invalidItems = orderItems.filter(item => !item.item_name);
    if (invalidItems.length > 0) {
      console.error('[Inventory] Some items are missing required fields:', invalidItems);
      toast.error('Some items are missing required information. Please try again.');
      return;
    }

    // Log the final data being sent
    console.log('[Inventory] Final order data being sent to API:', {
      orderData: dbOrderData,
      items: orderItems
    });

    // Create a variable to track if the operation completed normally
    let operationCompleted = false;

    // Add a timeout to ensure the dialog closes even if there are issues
    const safetyTimeout = setTimeout(() => {
      console.log('[Inventory] Safety timeout triggered for single property order - closing dialog');
      if (!operationCompleted) {
        purchaseOrderDialog.onClose();
        toast.warning('Purchase order may have been created, but the process took longer than expected. Please check the Purchase Orders page.');
        navigate('/purchase-orders');
      }
    }, 10000); // 10 seconds timeout

    // Call the API to create the purchase order
    createPurchaseOrder.mutate({
      orderData: dbOrderData,
      items: orderItems
    }, {
      onSuccess: () => {
        console.log('[Inventory] Purchase order created successfully');
        operationCompleted = true;
        clearTimeout(safetyTimeout);
        purchaseOrderDialog.onClose();
        toast.success('Purchase order created successfully');

        // Navigate to the purchase orders page
        navigate('/purchase-orders');
      },
      onError: (error) => {
        console.error('[Inventory] Error creating purchase order:', error);
        operationCompleted = true;
        clearTimeout(safetyTimeout);

        // Provide more helpful error messages
        if (error.message.includes('property_id')) {
          toast.error('Invalid property selected. Please select a different property.');
        } else if (error.message.includes('item_name')) {
          toast.error('One or more items have invalid names. Please try again.');
        } else if (error.message.includes('quantity')) {
          toast.error('One or more items have invalid quantities. Please try again.');
        } else {
          toast.error(`Failed to create purchase order: ${error.message}`);
        }

        // Close the dialog even on error after a short delay
        setTimeout(() => {
          purchaseOrderDialog.onClose();
        }, 2000);
      }
    });
  };

  return (
    <PageTransition>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-24">
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <div className="flex items-center justify-between">
            <div>
              {/* Title is only shown once at the top level */}
              <h1 className="text-3xl font-bold flex items-center">
                <Package className="mr-2 h-8 w-8 text-primary" />
                Inventory Management
              </h1>
              <p className="text-muted-foreground mt-1">
                Manage inventory items across your properties
              </p>
            </div>
            <TabsList>
              <TabsTrigger value="inventory">Inventory</TabsTrigger>
              <TabsTrigger value="amazon">Amazon Search</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="inventory" className="space-y-6">
            <InventoryHeader
              onAddItem={handleAddItem}
              onCreateOrder={handleCreatePurchaseOrder}
              onBulkImport={handleBulkImport}
              setSearchQuery={setSearchQuery}
              searchQuery={searchQuery}
              toggleFilters={toggleFilters}
              onRefresh={() => {
                console.log('[Inventory] Manual refresh triggered');
                refetchProperties();
                retryFetch();
              }}
              isLoading={isLoading || isLoadingProperties}
            />

            <InventorySummary
              items={formattedItems || []}
              filteredItems={filteredItems}
              isLoading={isLoading}
            />

            {showFilters && (
              <InventoryFilterBar
                filters={filters}
                setFilters={setFilters}
                properties={properties}
                collections={collections}
              />
            )}

            <InventoryContent
              isLoading={isLoading}
              filteredItems={filteredItems}
              onItemClick={handleItemClick}
              onDeleteItems={handleDeleteItems}
              searchQuery={searchQuery}
              filters={filters}
              retryFetch={retryFetch}
              error={error}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
          </TabsContent>

          <TabsContent value="amazon" className="space-y-4">
            <AmazonSearch />
          </TabsContent>
        </Tabs>

        <InventoryDialog
          isOpen={itemDialog.isOpen}
          onClose={itemDialog.onClose}
          item={selectedItem}
          onSave={handleSaveItem}
          properties={properties}
        />

        <CreatePurchaseOrderDialog
          isOpen={purchaseOrderDialog.isOpen}
          onClose={purchaseOrderDialog.onClose}
          items={formattedItems}
          properties={properties}
          onSave={handleSavePurchaseOrder}
        />

        <BulkImportDialog
          isOpen={bulkImportDialog.isOpen}
          onClose={() => {
            console.log('BulkImportDialog onClose called');
            bulkImportDialog.onClose();
            setExtensionImportData(null); // Clear data when dialog closes

            // Force close as a backup
            forceCloseDialog(() => {
              bulkImportDialog.onClose();
              setExtensionImportData(null);
            }, 1000, 'Force closing from Inventory component');
          }}
          onImport={handleBulkSaveItems}
          properties={properties}
          // Pass the extension data as props
          items={extensionImportData?.items}
          isExtensionImport={extensionImportData?.isExtensionImport}
        />
      </div>
    </PageTransition>
  );
};

export default Inventory;
