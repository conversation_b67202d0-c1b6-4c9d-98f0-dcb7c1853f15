import { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';
import { InventoryItem, FormattedInventoryItem } from '@/types/inventory';
import { processAndUploadImage, createInventoryBuckets } from '@/utils/imageProcessing';

export interface InventoryData {
  inventoryItems: FormattedInventoryItem[];
  loading: boolean;
  error: string | null;
  isError: boolean;
  fetchInventory: () => Promise<void>;
  addInventoryItem: (item: Omit<FormattedInventoryItem, 'id'>) => Promise<boolean>;
  updateInventoryItem: (id: string, item: Partial<FormattedInventoryItem>) => Promise<boolean>;
  deleteInventoryItem: (id: string) => Promise<boolean>;
  bulkAddInventoryItems: (items: FormattedInventoryItem[]) => Promise<boolean>;
}

/**
 * A standardized hook for fetching inventory items using React Query
 * This follows the same pattern as useOperationsDataQuery for consistency
 */
export const useInventoryQueryV2 = (): InventoryData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);
  const { isImpersonating } = useImpersonation();

  // Ensure storage buckets are created
  useCallback(() => {
    createInventoryBuckets()
      .then(success => {
        if (success) {
          console.log('[useInventoryQueryV2] Storage buckets verified');
        } else {
          console.warn('[useInventoryQueryV2] Storage buckets could not be verified');
        }
      })
      .catch(error => {
        console.error('[useInventoryQueryV2] Error checking storage buckets:', error);
      });
  }, [])();

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[useInventoryQueryV2] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['inventoryV2'] });
    await queryClient.refetchQueries({ queryKey: ['inventoryV2'] });
  }, [queryClient]);

  // Fetch inventory items
  const {
    data: inventoryItems = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['inventoryV2'],
    queryFn: async () => {
      try {
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log(`[useInventoryQueryV2] Fetching inventory items (attempt ${retryCount + 1})`);

        // Try to use RPC function first
        try {
          const { data: rpcData, error: rpcError } = await supabase.rpc(
            'get_user_inventory_items',
            { p_user_id: userId }
          );

          if (rpcError) {
            console.error('[useInventoryQueryV2] RPC function error:', rpcError);
            // Don't throw, we'll use the fallback method
            throw rpcError;
          }

          if (!rpcData || rpcData.length === 0) {
            console.log('[useInventoryQueryV2] No inventory items found via RPC');
            return [];
          }

          console.log(`[useInventoryQueryV2] Successfully loaded ${rpcData.length} inventory items via RPC`);

          // Apply impersonation filter if needed
          let filteredItems = rpcData;
          if (isImpersonating) {
            console.log('[useInventoryQueryV2] Impersonating user, filtering inventory items');
            filteredItems = rpcData.filter(item => item.user_id === userId);
          }

          // Format the data to match the expected structure in the component
          return filteredItems.map(item => ({
            id: item.id,
            name: item.name || 'Unnamed Item',
            propertyId: item.property_id || '',
            propertyName: item.property_name || '',
            collection: item.collection || '',
            quantity: typeof item.quantity === 'number' ? item.quantity : 0,
            minQuantity: typeof item.min_quantity === 'number' ? item.min_quantity : 1,
            price: item.price,
            amazonUrl: item.amazon_url || '',
            walmartUrl: item.walmart_url || '',
            imageUrl: item.image_url || '',
            lastOrdered: item.last_ordered
          }));
        } catch (rpcError) {
          // Fallback to direct query if RPC fails
          console.log('[useInventoryQueryV2] Falling back to direct query method');

          // Get inventory items directly
          const { data: inventoryData, error: inventoryError } = await supabase
            .from('inventory_items')
            .select(`
              id,
              name,
              property_id,
              collection,
              quantity,
              min_quantity,
              price,
              amazon_url,
              walmart_url,
              image_url,
              last_ordered,
              user_id,
              properties(name)
            `)
            .eq('user_id', userId)
            .order('name');

          if (inventoryError) {
            console.error('[useInventoryQueryV2] Error fetching inventory items directly:', inventoryError);
            throw inventoryError;
          }

          if (!inventoryData || inventoryData.length === 0) {
            console.log('[useInventoryQueryV2] No inventory items found via direct query');
            return [];
          }

          console.log(`[useInventoryQueryV2] Successfully loaded ${inventoryData.length} inventory items via direct query`);

          // Format the data to match the expected structure
          const formattedItems = inventoryData.map(item => ({
            id: item.id,
            name: item.name || 'Unnamed Item',
            property_id: item.property_id || '',
            property_name: item.properties?.name || '',
            collection: item.collection || '',
            quantity: typeof item.quantity === 'number' ? item.quantity : 0,
            min_quantity: typeof item.min_quantity === 'number' ? item.min_quantity : 1,
            price: item.price,
            amazon_url: item.amazon_url || '',
            walmart_url: item.walmart_url || '',
            image_url: item.image_url || '',
            last_ordered: item.last_ordered,
            user_id: item.user_id
          }));

          // Apply impersonation filter if needed
          let filteredItems = formattedItems;
          if (isImpersonating) {
            console.log('[useInventoryQueryV2] Impersonating user, filtering inventory items');
            filteredItems = formattedItems.filter(item => item.user_id === userId);
          }

          // Format the data to match the expected structure in the component
          return filteredItems.map(item => ({
            id: item.id,
            name: item.name || 'Unnamed Item',
            propertyId: item.property_id || '',
            propertyName: item.property_name || '',
            collection: item.collection || '',
            quantity: typeof item.quantity === 'number' ? item.quantity : 0,
            minQuantity: typeof item.min_quantity === 'number' ? item.min_quantity : 1,
            price: item.price,
            amazonUrl: item.amazon_url || '',
            walmartUrl: item.walmart_url || '',
            imageUrl: item.image_url || '',
            lastOrdered: item.last_ordered
          }));
        }

        // This code should never be reached since we're returning from the try/catch blocks above
        return [];
      } catch (err: any) {
        console.error('[useInventoryQueryV2] Error fetching inventory items:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    keepPreviousData: true, // Added to preserve data during refetching
    enabled: !!userId,
    networkMode: 'always'
  });

  // Add inventory item mutation
  const addItemMutation = useMutation({
    mutationFn: async (item: Omit<FormattedInventoryItem, 'id'>) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log('[useInventoryQueryV2] Adding inventory item:', item);

      let processedImageUrl = item.imageUrl;
      if (item.imageUrl && !item.hasProcessedImage) {
        try {
          console.log('[useInventoryQueryV2] Processing image:', item.imageUrl);
          processedImageUrl = await processAndUploadImage(item.imageUrl);
          console.log('[useInventoryQueryV2] Processed image URL:', processedImageUrl);
        } catch (imageError) {
          console.error('[useInventoryQueryV2] Image processing error:', imageError);
          processedImageUrl = item.imageUrl;
        }
      }

      const { data, error } = await supabase
        .from('inventory_items')
        .insert({
          name: item.name,
          property_id: item.propertyId,
          collection: item.collection,
          quantity: item.quantity,
          min_quantity: item.minQuantity,
          price: item.price,
          amazon_url: item.amazonUrl,
          walmart_url: item.walmartUrl,
          image_url: processedImageUrl,
          user_id: userId
        })
        .select();

      if (error) {
        console.error('[useInventoryQueryV2] Error adding inventory item:', error);
        throw error;
      }

      return data?.[0] || null;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventoryV2'] });
    }
  });

  // Update inventory item mutation
  const updateItemMutation = useMutation({
    mutationFn: async ({ id, item }: { id: string, item: Partial<FormattedInventoryItem> }) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useInventoryQueryV2] Updating inventory item ${id}:`, item);

      let processedImageUrl = item.imageUrl;
      if (item.imageUrl && !item.hasProcessedImage) {
        try {
          console.log('[useInventoryQueryV2] Processing image:', item.imageUrl);
          processedImageUrl = await processAndUploadImage(item.imageUrl);
          console.log('[useInventoryQueryV2] Processed image URL:', processedImageUrl);
        } catch (imageError) {
          console.error('[useInventoryQueryV2] Image processing error:', imageError);
          processedImageUrl = item.imageUrl;
        }
      }

      const { data, error } = await supabase
        .from('inventory_items')
        .update({
          name: item.name,
          property_id: item.propertyId,
          collection: item.collection,
          quantity: item.quantity,
          min_quantity: item.minQuantity,
          price: item.price,
          amazon_url: item.amazonUrl,
          walmart_url: item.walmartUrl,
          image_url: processedImageUrl
        })
        .eq('id', id)
        .eq('user_id', userId)
        .select();

      if (error) {
        console.error('[useInventoryQueryV2] Error updating inventory item:', error);
        throw error;
      }

      return data?.[0] || null;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventoryV2'] });
    }
  });

  // Delete inventory item mutation
  const deleteItemMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useInventoryQueryV2] Deleting inventory item ${id}`);

      // First check if the item exists and belongs to the user
      const { data: existingItem, error: checkError } = await supabase
        .from('inventory_items')
        .select('id, name')
        .eq('id', id)
        .eq('user_id', userId)
        .single();

      if (checkError) {
        console.error('[useInventoryQueryV2] Error checking item existence:', checkError);
        throw new Error(`Item not found or access denied: ${checkError.message}`);
      }

      if (!existingItem) {
        throw new Error('Item not found or you do not have permission to delete it');
      }

      // Proceed with deletion
      const { error } = await supabase
        .from('inventory_items')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        console.error('[useInventoryQueryV2] Error deleting inventory item:', error);
        throw new Error(`Failed to delete item: ${error.message}`);
      }

      console.log(`[useInventoryQueryV2] Successfully deleted item: ${existingItem.name}`);
      return { id, name: existingItem.name };
    },
    onSuccess: (data) => {
      console.log(`[useInventoryQueryV2] Delete mutation succeeded for item: ${data.name}`);
      queryClient.invalidateQueries({ queryKey: ['inventoryV2'] });
    },
    onError: (error) => {
      console.error('[useInventoryQueryV2] Delete mutation failed:', error);
    }
  });

  // Bulk add inventory items mutation
  const bulkAddItemsMutation = useMutation({
    mutationFn: async (items: FormattedInventoryItem[]) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useInventoryQueryV2] Bulk adding ${items.length} inventory items`);

      const formattedItems = await Promise.all(items.map(async (item) => {
        let processedImageUrl = item.imageUrl;
        if (item.imageUrl && !item.hasProcessedImage) {
          try {
            processedImageUrl = await processAndUploadImage(item.imageUrl);
          } catch (imageError) {
            console.error('[useInventoryQueryV2] Image processing error:', imageError);
            processedImageUrl = item.imageUrl;
          }
        }

        return {
          name: item.name,
          property_id: item.propertyId,
          collection: item.collection,
          quantity: item.quantity,
          min_quantity: item.minQuantity,
          price: item.price,
          amazon_url: item.amazonUrl,
          walmart_url: item.walmartUrl,
          image_url: processedImageUrl,
          user_id: userId
        };
      }));

      const { error } = await supabase
        .from('inventory_items')
        .insert(formattedItems);

      if (error) {
        console.error('[useInventoryQueryV2] Error bulk adding inventory items:', error);
        throw error;
      }

      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventoryV2'] });
    }
  });

  // Wrapper functions for mutations
  const addInventoryItem = async (item: Omit<FormattedInventoryItem, 'id'>): Promise<boolean> => {
    try {
      await addItemMutation.mutateAsync(item);
      return true;
    } catch (error) {
      return false;
    }
  };

  const updateInventoryItem = async (id: string, item: Partial<FormattedInventoryItem>): Promise<boolean> => {
    try {
      await updateItemMutation.mutateAsync({ id, item });
      return true;
    } catch (error) {
      return false;
    }
  };

  const deleteInventoryItem = async (id: string): Promise<boolean> => {
    try {
      console.log(`[useInventoryQueryV2] Attempting to delete item ${id}`);
      await deleteItemMutation.mutateAsync(id);
      console.log(`[useInventoryQueryV2] Successfully deleted item ${id}`);
      return true;
    } catch (error) {
      console.error(`[useInventoryQueryV2] Failed to delete item ${id}:`, error);
      return false;
    }
  };

  const bulkAddInventoryItems = async (items: FormattedInventoryItem[]): Promise<boolean> => {
    try {
      await bulkAddItemsMutation.mutateAsync(items);
      return true;
    } catch (error) {
      return false;
    }
  };

  // Add error retry effect similar to usePurchaseOrders
  useEffect(() => {
    // Only retry on actual errors, not on empty data
    if (isError) {
      const timer = setTimeout(() => {
        if (retryCount < 3) {
          console.log('[useInventoryQueryV2] Auto-retrying data fetch due to error');
          retryFetch();
        } else {
          console.error('[useInventoryQueryV2] Failed to load inventory items after multiple attempts');
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isError, retryCount, retryFetch]);

  return {
    inventoryItems,
    loading: isLoading,
    error: error ? String(error) : null,
    isError,
    fetchInventory: retryFetch,
    addInventoryItem,
    updateInventoryItem,
    deleteInventoryItem,
    bulkAddInventoryItems
  };
};
