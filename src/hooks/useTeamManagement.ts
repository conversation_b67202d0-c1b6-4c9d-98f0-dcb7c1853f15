import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/supabase';
import emailService from '@/services/emailService';

export type TeamMember = {
  id: string;
  user_id: string;
  team_id: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  status: string;
  avatar_url?: string | null;
  created_at: string;
  /** Team member role (comes from joined profile table) */
  profile_role?: UserRole;
};

export type Team = {
  id: string;
  name: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
  member_count?: number;
};

export const useTeamManagement = () => {
  const { authState } = useAuth();
  const [loading, setLoading] = useState(false);
  const [teams, setTeams] = useState<Team[]>([]);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [teamProperties, setTeamProperties] = useState<string[]>([]);
  const [sentInvitations, setSentInvitations] = useState<any[]>([]);
  const [loadingSentInvitations, setLoadingSentInvitations] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [dataInitialized, setDataInitialized] = useState(false);

  // Automatically fetch teams when the hook is initialized and user is authenticated
  useEffect(() => {
    if (authState.user?.id && !dataInitialized) {
      fetchUserTeams();
      setDataInitialized(true);
    }
  }, [authState.user?.id, dataInitialized]);

  // Define fetchTeamMembers with useCallback to avoid circular dependencies
  const fetchTeamMembers = useCallback(async (teamId: string) => {
    if (!teamId) return;

    // Clear existing team members to avoid showing stale data
    setTeamMembers([]);
    setLoading(true);
    try {
      console.log('Fetching team members for team:', teamId);

      // Direct query to team_members with profiles join
      const { data: joinData, error: joinError } = await supabase
        .from('team_members')
        .select(`
          *,
          profiles:user_id (
            id,
            email,
            first_name,
            last_name,
            avatar_url,
            role
          )
        `)
        .eq('team_id', teamId);

      if (joinError) {
        console.error('Error in direct team_members query:', joinError);
        throw joinError;
      }

      console.log('Team members found via direct query:', joinData?.length || 0, joinData);

      const formattedMembers = joinData.map((member: any) => ({
        id: member.id,
        user_id: member.user_id,
        team_id: member.team_id,
        email: member.profiles?.email,
        first_name: member.profiles?.first_name,
        last_name: member.profiles?.last_name,
        status: member.status,
        avatar_url: member.profiles?.avatar_url,
        created_at: member.created_at,
        profile_role: member.profiles?.role
      }));

      console.log('Formatted team members:', formattedMembers);
      setTeamMembers(formattedMembers);
    } catch (error: any) {
      console.error('Error fetching team members:', error);
      setTimeout(() => toast.error('Failed to load team members'), 0);
      throw error; // Re-throw to allow caller to handle
    } finally {
      setLoading(false);
    }
  }, []);

  // Define fetchTeamProperties with useCallback to avoid circular dependencies
  const fetchTeamProperties = useCallback(async (teamId: string) => {
    if (!teamId) return;

    setLoading(true);
    try {
      console.log('Fetching properties for team:', teamId);

      // Use the get_team_properties function to bypass RLS
      const { data, error } = await supabase
        .rpc('get_team_properties', {
          p_team_id: teamId
        });

      if (error) {
        console.error('Error fetching team properties from database:', error);
        throw error;
      }

      // The function now returns an array of UUIDs directly
      const teamPropertyIds = data || [];

      console.log('Team properties found:', teamPropertyIds.length, teamPropertyIds);
      setTeamProperties(teamPropertyIds);
    } catch (error: any) {
      console.error('Error fetching team properties:', error);
      setTimeout(() => toast.error('Failed to load team properties'), 0);
    } finally {
      setLoading(false);
    }
  }, []);

  // Track if we've already fetched data for the selected team
  const [initialFetchDone, setInitialFetchDone] = useState(false);

  // Automatically fetch team members and properties when a team is selected
  useEffect(() => {
    if (selectedTeam?.id && !initialFetchDone) {
      console.log('Team selected, initial fetch for team:', selectedTeam.id);
      setInitialFetchDone(true);

      // Automatically fetch team members when a team is selected
      // This ensures team members are loaded immediately when a team is selected
      fetchTeamMembers(selectedTeam.id)
        .then(() => {
          console.log('Initial team members fetch successful for team:', selectedTeam.id);
        })
        .catch(err => {
          console.error('Error in initial team members fetch:', err);
        });
    }
  }, [selectedTeam?.id, initialFetchDone, fetchTeamMembers]);

  const fetchUserTeams = async () => {
    if (!authState.user?.id) return;

    setLoading(true);
    try {
      // Check if user is a property manager
      const isPropertyManager = authState.profile?.role === 'property_manager';
      const isAdmin = authState.profile?.role === 'admin' || authState.profile?.is_super_admin;

      // For property managers in production, use the Edge Function
      if ((isPropertyManager || isAdmin) && process.env.NODE_ENV === 'production') {
        console.log('[useTeamManagement] Property manager in production, using Edge Function');
        try {
          const { data: edgeData, error: edgeError } = await supabase.functions.invoke('get-property-manager-teams', {
            body: { userId: authState.user.id }
          });

          if (edgeError) {
            console.error('[useTeamManagement] Edge function error:', edgeError);
            throw edgeError;
          }

          if (edgeData && edgeData.length > 0) {
            console.log(`[useTeamManagement] Edge function found ${edgeData.length} teams`);
            setTeams(edgeData);

            if (!selectedTeam && edgeData.length > 0) {
              setSelectedTeam(edgeData[0]);
            }
            setLoading(false);
            return;
          }
        } catch (edgeErr) {
          console.error('[useTeamManagement] Error calling Edge function:', edgeErr);
          // Continue with regular approach as fallback
        }
      }

      // Regular approach as fallback
      const { data: ownedTeams, error: ownedError } = await supabase
        .from('teams')
        .select('*, team_members:team_members(count)')
        .eq('owner_id', authState.user.id);

      if (ownedError) throw ownedError;

      const { data: memberTeams, error: memberError } = await supabase
        .from('teams')
        .select('*, team_members:team_members(count)')
        .neq('owner_id', authState.user.id)
        .in('id', await getTeamMemberships(authState.user.id));

      if (memberError) throw memberError;

      const allTeams = [...(ownedTeams || []), ...(memberTeams || [])];
      const teamsWithMemberCount = allTeams.map(team => ({
        ...team,
        member_count: team.team_members?.[0]?.count || 0
      }));

      setTeams(teamsWithMemberCount);

      if (!selectedTeam && teamsWithMemberCount.length > 0) {
        setSelectedTeam(teamsWithMemberCount[0]);
      }
    } catch (error: any) {
      console.error('Error fetching teams:', error);
      setTimeout(() => toast.error('Failed to load teams'), 0);
    } finally {
      setLoading(false);
    }
  };

  const getTeamMemberships = async (userId: string): Promise<string[]> => {
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId)
        .eq('status', 'active');

      if (error) throw error;

      const teamIds = (data || []).map((item: any) => item.team_id);
      return teamIds;
    } catch (error) {
      console.error('Error getting team memberships:', error);
      return [];
    }
  };

  const ensureUserProfile = async () => {
    if (!authState.user?.id) return false;

    try {
      const { data: existingProfile, error: checkError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', authState.user.id)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking profile:', checkError);
        return false;
      }

      if (!existingProfile) {
        console.log('Creating minimal profile with admin status:', Boolean(authState.profile?.is_super_admin));

        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: authState.user.id,
            email: authState.user.email || authState.profile?.email || '',
            first_name: authState.profile?.first_name || null,
            last_name: authState.profile?.last_name || null,
            role: authState.profile?.role || 'property_manager',
            is_super_admin: Boolean(authState.profile?.is_super_admin)
          });

        if (insertError) {
          console.error('Error creating profile:', insertError);
          return false;
        }
      }

      return true;
    } catch (error: any) {
      console.error('Error in ensureUserProfile:', error);
      return false;
    }
  };



  const generateInvitationToken = (): string => {
    return crypto.randomUUID();
  };

  const inviteUserToTeam = async (teamId: string, email: string, role: UserRole) => {
    if (!authState.user?.id || !teamId) {
      setTimeout(() => toast.error('Unable to send invitation at this time'), 0);
      return false;
    }

    setLoading(true);
    try {
      const invitationToken = generateInvitationToken();

      console.log('Invitation parameters:', {
        teamId,
        email,
        role,
        invitedBy: authState.user.id,
        token: invitationToken
      });

      console.log('Attempting to create invitation with:', {
        teamId,
        email,
        role,
        invitedBy: authState.user.id,
        token: invitationToken
      });

      const { data, error } = await supabase.functions.invoke('create-team-invitation', {
        body: {
          teamId,
          email,
          role: String(role),
          invitedBy: authState.user.id,
          token: invitationToken
        }
      });

      if (error) {
        console.error('Error creating invitation:', {
          error,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      console.log('Invitation created successfully:', data);

      // Verify invitation exists in database
      const { data: verifyData, error: verifyError } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('token', invitationToken)
        .single();

      if (verifyError || !verifyData) {
        console.error('Failed to verify invitation creation:', {
          verifyError,
          verifyData
        });
      } else {
        console.log('Verified invitation exists:', verifyData);
      }

      const { data: teamData } = await supabase
        .from('teams')
        .select('name')
        .eq('id', teamId)
        .single();

      const inviterName = `${authState.profile?.first_name || ''} ${authState.profile?.last_name || ''}`.trim() || authState.profile?.email || 'A user';
      const teamName = teamData?.name || 'a team';

      // Try to send email but don't fail the invitation if email fails
      try {
        const emailResult = await emailService.sendTeamInvitationEmail(
          email,
          inviterName,
          teamName,
          role,
          invitationToken
        );

        if (!emailResult.success && emailResult.error) {
          console.error('Failed to send invitation email:', emailResult.error);
          setTimeout(() => toast.warning('Invitation created, but email delivery failed. The person may not receive a notification.'), 0);
        } else {
          // If we have an error but success is true, it's our fallback case
          if (emailResult.error) {
            console.warn('Email fallback used:', emailResult.error);
            setTimeout(() => toast.success('Invitation created successfully. In development mode, emails are not actually sent.'), 0);
          } else {
            setTimeout(() => toast.success('Invitation sent successfully'), 0);
          }
        }
      } catch (emailError) {
        console.error('Error sending invitation email:', emailError);
        setTimeout(() => toast.warning('Invitation created, but there was an issue sending the email notification.'), 0);
      }

      // Always return true since the invitation was created in DB
      return true;
    } catch (error: any) {
      console.error('Error inviting user to team:', error);
      setTimeout(() => toast.error('Failed to send invitation: ' + error.message), 0);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const getInvitationDetails = async (token: string) => {
    try {
      console.log('Getting invitation details for token:', token);

      const { data, error } = await supabase
        .from('team_invitations')
        .select('*, teams:team_id(name)')
        .eq('token', token)
        .maybeSingle();

      if (error) {
        console.error('Error fetching invitation:', error);
        return {
          exists: false,
          error: error.message || 'Error fetching invitation details',
          invitation: null
        };
      }

      if (!data) {
        console.log('No invitation found with token:', token);
        return {
          exists: false,
          error: 'Invitation not found',
          invitation: null
        };
      }

      if (data.status !== 'pending') {
        console.log(`Invitation status is ${data.status}, not pending`);
        return {
          exists: false,
          error: `Invitation has already been ${data.status}`,
          invitation: null
        };
      }

      if (data.expires_at && new Date(data.expires_at) < new Date()) {
        console.log('Invitation has expired:', data.expires_at);
        return {
          exists: false,
          error: 'Invitation has expired',
          invitation: null
        };
      }

      console.log('Found valid invitation details:', data);

      return {
        exists: true,
        error: null,
        invitation: data
      };
    } catch (error: any) {
      console.error('Error checking invitation:', error);
      return {
        exists: false,
        error: error.message || 'An unexpected error occurred',
        invitation: null
      };
    }
  };

  const acceptTeamInvitation = async (token: string) => {
    setLoading(true);
    try {
      if (!authState.user?.id) {
        console.log(`[Accept Invite ${token}] No authenticated user, returning requiresAuth=true`);
        return {
          success: false,
          error: 'You must be logged in to accept an invitation',
          requiresAuth: true,
          invitation: null
        };
      }

      console.log(`[Accept Invite ${token}] User authenticated as ${authState.user.email}, proceeding with invitation acceptance`);

      // Ensure user profile exists before calling the function (optional but good practice)
      const profileEnsured = await ensureUserProfile();
      if (!profileEnsured) {
         console.error(`[Accept Invite ${token}] Failed to ensure user profile exists for user ${authState.user.id}`);
         return {
           success: false,
           error: 'Failed to prepare user profile for team joining.',
           requiresAuth: false,
           invitation: null // We don't have invitation details yet
         };
      }

      console.log(`[Accept Invite ${token}] User profile ensured, proceeding with invitation acceptance`);

      // Get the invitation details to check the email and team information
      const { data: invitationData, error: invitationError } = await supabase
        .from('team_invitations')
        .select('email, role, team_id, team_name, status')
        .eq('token', token)
        .maybeSingle();

      if (invitationError) {
        console.error(`[Accept Invite ${token}] Error fetching invitation details:`, invitationError);
      } else if (invitationData) {
        console.log(`[Accept Invite ${token}] Invitation details:`, invitationData);

        // Log if there's an email mismatch
        if (invitationData.email !== authState.user.email) {
          console.log(`[Accept Invite ${token}] Note: Invitation email (${invitationData.email}) doesn't match user email (${authState.user.email})`);
          console.log('The backend function will handle this mismatch');
        }

        // Store the team ID for later use
        if (invitationData.team_id) {
          localStorage.setItem('lastAcceptedTeamId', invitationData.team_id);
        }
      }

      console.log(`[Accept Invite ${token}] Calling RPC accept_invitation_and_add_member for user ${authState.user.id}`);

      // Call the PostgreSQL function to accept invitation and add member
      console.log(`Accepting invitation with token ${token} for user ${authState.user.id}`);

      const { data: rpcData, error: rpcError } = await supabase.rpc(
        'accept_invitation_and_add_member',
        {
          p_token: token,
          p_user_id: authState.user.id
        }
      );

      // Log the result for debugging
      console.log('RPC result:', rpcData, 'Error:', rpcError);

      if (rpcError) {
        console.error(`[Accept Invite ${token}] RPC error:`, rpcError);
        return {
          success: false,
          error: 'Failed to accept invitation: ' + (rpcError.message || 'Database error'),
          requiresAuth: false,
          invitation: null // We don't have invitation details in case of RPC error
        };
      }

      // The function returns a JSON object with success status and potential error message
      if (!rpcData || !rpcData.success) {
        console.error(`[Accept Invite ${token}] Function returned error:`, rpcData?.error);
        // Attempt to fetch invitation details for context, even on failure
        const { data: failedInvite } = await supabase
          .from('team_invitations')
          .select('*, teams:team_id(name)')
          .eq('token', token)
          .maybeSingle();

        return {
          success: false,
          error: rpcData?.error || 'Invitation could not be accepted.',
          requiresAuth: false,
          invitation: failedInvite || null
        };
      }

      console.log(`[Accept Invite ${token}] RPC success:`, rpcData);

      // Fetch team name for the success toast, as the RPC returns team_id
      const { data: teamData } = await supabase
        .from('teams')
        .select('name')
        .eq('id', rpcData.team_id)
        .single();

      console.log('Team data after accepting invitation:', teamData);
      setTimeout(() => toast.success(`You've joined ${teamData?.name || 'the team'}`), 0);

      // Fetch full invitation details to return, matching original structure
      const { data: finalInvitation } = await supabase
        .from('team_invitations')
        .select('*, teams:team_id(name)')
        .eq('token', token)
        .maybeSingle();

      console.log('Final invitation data:', finalInvitation);

      // Refresh team members list
      if (rpcData.team_id) {
        try {
          await fetchTeamMembers(rpcData.team_id);
        } catch (refreshError) {
          console.error('Error refreshing team members after accepting invitation:', refreshError);
        }
      }

      // Clear any pending invitation data from localStorage
      localStorage.removeItem('pendingInvitation');
      localStorage.removeItem('pendingInvitationEmail');
      localStorage.removeItem('pendingInvitationTeamId');
      localStorage.removeItem('pendingInvitationRole');
      localStorage.removeItem('pendingInvitationTeamName');

      return {
        success: true,
        error: null,
        requiresAuth: false,
        invitation: finalInvitation, // Return the updated invitation details
        teamId: rpcData.team_id // Include the team ID for easier navigation
      };
    } catch (error: any) {
      // General catch block for unexpected errors (e.g., network issues before RPC call)
      console.error(`[Accept Invite ${token}] Unexpected error in acceptTeamInvitation wrapper:`, error);
      setTimeout(() => toast.error('Failed to accept invitation: ' + error.message), 0);
      return {
        success: false,
        error: error.message,
        requiresAuth: false,
        invitation: null
      };
    } finally {
      setLoading(false);
    }
  };

  const createTeam = async (name: string) => {
    if (!authState.user?.id) {
      setTimeout(() => toast.error('You must be logged in to create a team'), 0);
      return null;
    }

    setLoading(true);
    try {
      const profileExists = await ensureUserProfile();
      if (!profileExists) {
        setTimeout(() => toast.error('Unable to create your user profile'), 0);
        return null;
      }

      const { data, error } = await supabase
        .from('teams')
        .insert({
          name,
          owner_id: authState.user.id
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating team:', error);
        throw error;
      }

      // Add the owner to the team_members table
      const { error: memberError } = await supabase
        .from('team_members')
        .insert({
          team_id: data.id,
          user_id: authState.user.id,
          added_by: authState.user.id,
          status: 'active'
        });

      if (memberError) {
        console.error('Error adding owner to team_members:', memberError);
        // Don't throw here, as the team was created successfully
        setTimeout(() => toast.warning('Team created, but there was an issue adding you as a member.'), 0);
      } else {
        setTimeout(() => toast.success('Team created successfully'), 0);
      }

      await fetchUserTeams();
      return data;
    } catch (error: any) {
      console.error('Error creating team:', error);
      setTimeout(() => toast.error('Failed to create team: ' + error.message), 0);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateTeam = async (teamId: string, name: string) => {
    if (!authState.user?.id) return false;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('teams')
        .update({ name })
        .eq('id', teamId)
        .eq('owner_id', authState.user.id);

      if (error) throw error;

      setTimeout(() => toast.success('Team updated successfully'), 0);
      await fetchUserTeams();
      return true;
    } catch (error: any) {
      console.error('Error updating team:', error);
      setTimeout(() => toast.error('Failed to update team'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const removeTeamMember = async (teamMemberId: string) => {
    if (!authState.user?.id) return false;

    setLoading(true);
    try {
      // First verify user has permission to manage staff for this team
      const { data: teamMember, error: fetchError } = await supabase
        .from('team_members')
        .select('team_id, user_id, profiles:user_id(role)')
        .eq('id', teamMemberId)
        .single();

      if (fetchError) throw fetchError;
      if (!teamMember?.team_id) throw new Error('Team member not found');

      // Don't allow removing admins or super admins unless you're a super admin
      if (teamMember.profiles?.role === 'admin' || teamMember.profiles?.role === 'super_admin') {
        const { data: currentUserProfile } = await supabase
          .from('profiles')
          .select('is_super_admin')
          .eq('id', authState.user.id)
          .single();

        if (!currentUserProfile?.is_super_admin) {
          throw new Error('You do not have permission to remove an admin');
        }
      }

      // With RLS policies in place, this will automatically check permissions
      const { error } = await supabase
        .from('team_members')
        .delete()
        .eq('id', teamMemberId);

      if (error) {
        if (error.code === 'PGRST301') {
          throw new Error('You do not have permission to remove this team member');
        }
        throw error;
      }

      // Also remove any permissions for this user in this team
      await supabase
        .from('user_permissions')
        .delete()
        .eq('user_id', teamMember.user_id)
        .eq('team_id', teamMember.team_id);

      setTimeout(() => toast.success('Team member removed successfully'), 0);
      if (selectedTeam) {
        await fetchTeamMembers(selectedTeam.id);
      }
      return true;
    } catch (error: any) {
      console.error('Error removing team member:', error);
      setTimeout(() => toast.error(error.message || 'Failed to remove team member'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  };



  const assignPropertyToTeam = async (teamId: string, propertyId: string) => {
    if (!authState.user?.id || !teamId || !propertyId) {
      setTimeout(() => toast.error('Missing required information'), 0);
      return false;
    }

    setLoading(true);
    try {
      console.log(`Calling add_property_to_team_direct with teamId=${teamId}, propertyId=${propertyId}, userId=${authState.user.id}`);

      // Use the direct SQL function to add property to team
      const { data, error } = await supabase.rpc(
        'add_property_to_team_direct',
        {
          p_team_id: teamId,
          p_property_id: propertyId,
          p_user_id: authState.user.id
        }
      );

      if (error) {
        console.error('Error from add_property_to_team_direct:', error);

        // Try a direct insert as a last resort
        console.log('Attempting direct insert as fallback...');

        try {
          // Check if user owns the property
          const { data: propertyData, error: propertyError } = await supabase
            .from('properties')
            .select('user_id')
            .eq('id', propertyId)
            .single();

          if (propertyError) throw propertyError;

          if (propertyData.user_id !== authState.user.id) {
            throw new Error('You can only assign properties you own to a team');
          }

          // Check if user owns the team
          const { data: teamData, error: teamError } = await supabase
            .from('teams')
            .select('owner_id')
            .eq('id', teamId)
            .single();

          if (teamError) throw teamError;

          if (teamData.owner_id !== authState.user.id) {
            throw new Error('You can only assign properties to teams you own');
          }

          // Direct insert
          const { error: insertError } = await supabase
            .from('team_properties')
            .insert({
              team_id: teamId,
              property_id: propertyId
            });

          if (insertError) {
            if (insertError.code === '23505') { // Unique violation
              console.log('Property is already assigned to this team');
            } else {
              throw insertError;
            }
          }
        } catch (fallbackError: any) {
          console.error('Fallback also failed:', fallbackError);
          throw fallbackError;
        }
      } else {
        console.log('Result from add_property_to_team_direct:', data);
      }

      console.log(`Successfully assigned property ${propertyId} to team ${teamId}`);
      setTimeout(() => toast.success('Property assigned to team successfully'), 0);

      // Refresh the team properties
      await fetchTeamProperties(teamId);
      return true;
    } catch (error: any) {
      console.error('Error assigning property to team:', error);
      setTimeout(() => toast.error(error.message || 'Failed to assign property to team'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const unassignPropertyFromTeam = async (teamId: string, propertyId: string) => {
    if (!authState.user?.id || !teamId || !propertyId) {
      setTimeout(() => toast.error('Missing required information'), 0);
      return false;
    }

    setLoading(true);
    try {
      console.log(`Calling remove_property_from_team_direct with teamId=${teamId}, propertyId=${propertyId}, userId=${authState.user.id}`);

      // Use the direct SQL function to remove property from team
      const { data, error } = await supabase.rpc(
        'remove_property_from_team_direct',
        {
          p_team_id: teamId,
          p_property_id: propertyId,
          p_user_id: authState.user.id
        }
      );

      if (error) {
        console.error('Error from remove_property_from_team_direct:', error);

        // Try a direct delete as a last resort
        console.log('Attempting direct delete as fallback...');

        try {
          // Check if the user owns the team
          const { data: teamData, error: teamError } = await supabase
            .from('teams')
            .select('owner_id')
            .eq('id', teamId)
            .single();

          if (teamError) throw teamError;

          if (teamData.owner_id !== authState.user.id) {
            throw new Error('You can only manage properties for teams you own');
          }

          // Direct delete
          const { error: deleteError } = await supabase
            .from('team_properties')
            .delete()
            .eq('team_id', teamId)
            .eq('property_id', propertyId);

          if (deleteError) {
            throw deleteError;
          }

          // Update inventory items
          await supabase
            .from('inventory_items')
            .update({ team_id: null })
            .eq('property_id', propertyId)
            .eq('team_id', teamId);

        } catch (fallbackError: any) {
          console.error('Fallback also failed:', fallbackError);
          throw fallbackError;
        }
      } else {
        console.log('Result from remove_property_from_team_direct:', data);
      }

      console.log(`Successfully removed property ${propertyId} from team ${teamId}`);
      setTimeout(() => toast.success('Property removed from team successfully'), 0);

      // Refresh the team properties
      await fetchTeamProperties(teamId);
      return true;
    } catch (error: any) {
      console.error('Error removing property from team:', error);
      setTimeout(() => toast.error(error.message || 'Failed to remove property from team'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const deleteTeam = async (teamId: string) => {
    if (!authState.user?.id || !teamId) {
      setTimeout(() => toast.error('Missing required information'), 0);
      return false;
    }

    setLoading(true);
    try {
      // Check if the user owns the team
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .select('owner_id, name')
        .eq('id', teamId)
        .single();

      if (teamError) throw teamError;

      if (teamData.owner_id !== authState.user.id) {
        throw new Error('You can only delete teams you own');
      }

      const confirmed = window.confirm(`Are you sure you want to delete the team "${teamData.name}"? This action cannot be undone. All team members will lose access to team properties.`);
      if (!confirmed) {
        setLoading(false);
        return false;
      }

      // Use the cascade function to delete the team and all related data
      const { error: deleteError } = await supabase.rpc('delete_team_cascade', {
        team_id_param: teamId
      });

      if (deleteError) {
        console.error('Error in delete_team_cascade function:', deleteError);
        toast.error(`Failed to delete team: ${deleteError.message}`, { id: 'delete-team' });

        // Fallback to direct SQL execution
        console.log('Falling back to direct SQL execution...');

        // Use a direct SQL query to delete everything in the correct order
        const { error: sqlError } = await supabase.functions.invoke('execute-sql', {
          body: {
            query: `
              -- Delete team members
              DELETE FROM team_members WHERE team_id = '${teamId}';

              -- Delete team_properties associations
              DELETE FROM team_properties WHERE team_id = '${teamId}';

              -- Update inventory items to remove team_id
              UPDATE inventory_items SET team_id = NULL WHERE team_id = '${teamId}';

              -- Delete user permissions for this team
              DELETE FROM user_permissions WHERE team_id = '${teamId}';

              -- Finally delete the team itself
              DELETE FROM teams WHERE id = '${teamId}';
            `
          }
        });

        if (sqlError) {
          console.error('Error executing SQL deletion:', sqlError);
          toast.error(`Failed to delete team: ${sqlError.message}`, { id: 'delete-team' });
          return false;
        }
      }

      setTimeout(() => toast.success('Team deleted successfully'), 0);
      await fetchUserTeams();
      return true;
    } catch (error: any) {
      console.error('Error deleting team:', error);
      setTimeout(() => toast.error(error.message || 'Failed to delete team'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fetch sent invitations for a specific team
  const fetchSentInvitations = useCallback(async (teamId: string) => {
    if (!authState.user?.id || !teamId) return;

    // Prevent duplicate fetches
    if (loadingSentInvitations) {
      console.log('Already loading sent invitations, skipping fetch');
      return;
    }

    setLoadingSentInvitations(true);
    try {
      console.log('Fetching sent invitations for team:', teamId);

      // Fetch invitations sent for this team
      const { data, error } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('team_id', teamId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log('Sent invitations:', data?.length || 0);
      setSentInvitations(data || []);
    } catch (error: any) {
      console.error('Error fetching sent invitations:', error);
      setTimeout(() => toast.error('Failed to load sent invitations'), 0);
    } finally {
      setLoadingSentInvitations(false);
    }
  }, [authState.user?.id, loadingSentInvitations]);

  // Delete an invitation
  const deleteInvitation = async (invitationId: string) => {
    if (!authState.user?.id) {
      setTimeout(() => toast.error('You must be logged in to delete invitations'), 0);
      return false;
    }

    setLoading(true);
    try {
      // Check if the user has permission to delete this invitation
      const { data: invitation, error: fetchError } = await supabase
        .from('team_invitations')
        .select('team_id, invited_by')
        .eq('id', invitationId)
        .single();

      if (fetchError) throw fetchError;

      // Check if user owns the team or is the one who sent the invitation
      const { data: team, error: teamError } = await supabase
        .from('teams')
        .select('owner_id')
        .eq('id', invitation.team_id)
        .single();

      if (teamError) throw teamError;

      const isTeamOwner = team.owner_id === authState.user.id;
      const isInviter = invitation.invited_by === authState.user.id;

      if (!isTeamOwner && !isInviter) {
        throw new Error('You do not have permission to delete this invitation');
      }

      // Delete the invitation
      const { error } = await supabase
        .from('team_invitations')
        .delete()
        .eq('id', invitationId);

      if (error) throw error;

      // Refresh the invitations list
      await fetchSentInvitations(invitation.team_id);

      setTimeout(() => toast.success('Invitation deleted successfully'), 0);
      return true;
    } catch (error: any) {
      console.error('Error deleting invitation:', error);
      setTimeout(() => toast.error(error.message || 'Failed to delete invitation'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Resend an invitation
  const resendInvitation = async (invitationId: string) => {
    if (!authState.user?.id) {
      setTimeout(() => toast.error('You must be logged in to resend invitations'), 0);
      return false;
    }

    setLoading(true);
    try {
      // Get the invitation details
      const { data: invitation, error: fetchError } = await supabase
        .from('team_invitations')
        .select('*, teams:team_id(name)')
        .eq('id', invitationId)
        .single();

      if (fetchError) throw fetchError;

      // Check if user owns the team or is the one who sent the invitation
      const { data: team, error: teamError } = await supabase
        .from('teams')
        .select('owner_id, name')
        .eq('id', invitation.team_id)
        .single();

      if (teamError) throw teamError;

      const isTeamOwner = team.owner_id === authState.user.id;
      const isInviter = invitation.invited_by === authState.user.id;

      if (!isTeamOwner && !isInviter) {
        throw new Error('You do not have permission to resend this invitation');
      }

      // Update the invitation's expiration date
      const { error: updateError } = await supabase
        .from('team_invitations')
        .update({
          expires_at: new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)).toISOString(), // 7 days from now
          updated_at: new Date().toISOString()
        })
        .eq('id', invitationId);

      if (updateError) throw updateError;

      // Send the invitation email
      try {
        const inviterName = `${authState.profile?.first_name || ''} ${authState.profile?.last_name || ''}`.trim() || authState.profile?.email || 'A user';
        const teamName = team.name || 'a team';

        const emailResult = await emailService.sendTeamInvitationEmail(
          invitation.email,
          inviterName,
          teamName,
          invitation.role,
          invitation.token
        );

        if (!emailResult.success && emailResult.error) {
          console.error('Failed to send invitation email:', emailResult.error);
          setTimeout(() => toast.warning('Invitation updated, but email delivery failed. The person may not receive a notification.'), 0);
        } else {
          setTimeout(() => toast.success('Invitation resent successfully'), 0);
        }
      } catch (emailError) {
        console.error('Error sending invitation email:', emailError);
        setTimeout(() => toast.warning('Invitation updated, but there was an issue sending the email notification.'), 0);
      }

      // Refresh the invitations list
      await fetchSentInvitations(invitation.team_id);
      return true;
    } catch (error: any) {
      console.error('Error resending invitation:', error);
      setTimeout(() => toast.error(error.message || 'Failed to resend invitation'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    teams,
    setTeams,
    teamMembers,
    teamProperties,
    sentInvitations,
    loadingSentInvitations,
    selectedTeam,
    setSelectedTeam,
    fetchUserTeams,
    createTeam,
    updateTeam,
    deleteTeam,
    fetchTeamMembers,
    fetchTeamProperties,
    fetchSentInvitations,
    assignPropertyToTeam,
    unassignPropertyFromTeam,
    inviteUserToTeam,
    acceptTeamInvitation,
    getInvitationDetails,
    removeTeamMember,
    deleteInvitation,
    resendInvitation
  };
};
