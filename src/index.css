
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 200 100% 40%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 200 100% 40%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 200 100% 40%;

    --radius: 0.5rem;

    --sidebar-background: 200 100% 40%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 200 100% 40%;
    --sidebar-accent: 200 90% 45%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 200 80% 35%;
    --sidebar-ring: 0 0% 100%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 200 100% 40%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 200 100% 40%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 200 100% 40%;

    --sidebar-background: 200 100% 40%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 200 100% 40%;
    --sidebar-accent: 200 90% 45%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 200 80% 35%;
    --sidebar-ring: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply scroll-smooth antialiased;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }
}

@layer utilities {
  .glass {
    @apply bg-white/40 dark:bg-black/40 backdrop-blur-lg border border-white/20 dark:border-white/10;
  }

  .glass-hover {
    @apply hover:bg-white/60 dark:hover:bg-black/60 transition-all duration-300;
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70;
  }

  .sidebar {
    @apply bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))] border-r border-[hsl(var(--sidebar-border))] shadow-lg;
  }

  .sidebar-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-md transition-colors;
  }

  .sidebar-item-active {
    @apply bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))];
  }

  .sidebar-item-inactive {
    @apply hover:bg-[hsl(var(--sidebar-accent))/20] text-[hsl(var(--sidebar-foreground))];
  }

  .stat-card {
    @apply bg-card rounded-md p-4 shadow-sm border border-border;
  }

  .stat-value {
    @apply text-2xl font-semibold text-card-foreground;
  }

  .stat-label {
    @apply text-sm text-muted-foreground;
  }

  .chart-container {
    @apply bg-card rounded-md p-4 shadow-sm border border-border;
  }

  .filter-dropdown {
    @apply bg-card border border-border rounded-md px-3 py-1.5 text-sm text-card-foreground;
  }
}

/* Compact mode */
.compact-mode .card {
  padding: 0.75rem;
}

.compact-mode .p-4 {
  padding: 0.5rem;
}

.compact-mode .p-6 {
  padding: 0.75rem;
}

.compact-mode .py-4 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.compact-mode .py-6 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.compact-mode .gap-6 {
  gap: 0.75rem;
}

.compact-mode .gap-4 {
  gap: 0.5rem;
}

.compact-mode .space-y-6 > * + * {
  margin-top: 0.75rem;
}

.compact-mode .space-y-4 > * + * {
  margin-top: 0.5rem;
}

.compact-mode .text-lg {
  font-size: 1rem; /* text-base size */
  line-height: 1.5rem; /* text-base line height */
}

.compact-mode .text-xl {
  font-size: 1.125rem; /* text-lg size */
  line-height: 1.75rem; /* text-lg line height */
}

.compact-mode .text-2xl {
  font-size: 1.25rem; /* text-xl size */
  line-height: 1.75rem; /* text-xl line height */
}

.compact-mode .mb-4 {
  margin-bottom: 0.5rem;
}

.compact-mode .mb-6 {
  margin-bottom: 0.75rem;
}

.compact-mode .mt-4 {
  margin-top: 0.5rem;
}

.compact-mode .mt-6 {
  margin-top: 0.75rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Sidebar scrollbar */
.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-thumb {
  @apply bg-white/30 rounded-full;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  @apply bg-white/50;
}

/* Page transitions */
.page-transition-enter {
  opacity: 0;
  transform: scale(0.98);
}

.page-transition-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: scale(1);
}

.page-transition-exit-active {
  opacity: 0;
  transform: scale(0.98);
  transition: opacity 300ms, transform 300ms;
}

/* Print styles */
@media print {
  body {
    background: white;
    font-size: 12pt;
    color: black;
  }

  .no-print, .no-print * {
    display: none !important;
  }

  .print-container {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    page-break-inside: auto;
  }

  tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
  }

  th {
    background-color: #f2f2f2;
    font-weight: bold;
  }

  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }

  img, svg {
    max-width: 100% !important;
  }

  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }

  thead {
    display: table-header-group;
  }

  tfoot {
    display: table-footer-group;
  }
}

/* PDF Export Styles */
.pdf-export-mode {
  background: white;
  padding: 20px;
}

.pdf-export-mode table {
  border-collapse: collapse;
  width: 100%;
  border-spacing: 0;
  margin-bottom: 20px;
}

.pdf-export-mode tr {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  display: table-row;
}

.pdf-export-mode th,
.pdf-export-mode td {
  padding: 8px;
  border: 1px solid #ddd;
  vertical-align: top;
}

.pdf-export-mode th {
  background-color: #f2f2f2;
  font-weight: bold;
  text-align: left;
}

.pdf-export-mode .no-print {
  display: none !important;
}

/* Ensure table headers repeat on each page */
.pdf-export-mode thead {
  display: table-header-group;
}

.pdf-export-mode tfoot {
  display: table-footer-group;
}

/* Add space between rows */
.pdf-export-mode tr.task-row {
  border-bottom: 2px solid #eee;
  margin-bottom: 10px;
  height: auto;
}

/* Ensure proper spacing between cells */
.pdf-export-mode td {
  padding-top: 8px;
  padding-bottom: 8px;
}

/* Ensure proper page breaks */
@media print {
  .pdf-export-mode tr {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  .pdf-export-mode thead {
    display: table-header-group;
  }

  .pdf-export-mode tfoot {
    display: table-footer-group;
  }
}

/* html2pdf specific styles */
.task-row {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

/* Add extra space after each row to prevent content from being cut off */
.task-row td {
  padding-bottom: 12px !important;
}

/* Ensure table headers repeat on each page for html2pdf */
thead {
  display: table-header-group;
}

tfoot {
  display: table-footer-group;
}

/* Add extra padding at the bottom of each page */
@page {
  margin-bottom: 20mm;
}

/* Styles for when PDF is being generated */
.generating-pdf .print-content {
  padding: 30px !important;
}

.generating-pdf table {
  border-collapse: collapse !important;
  width: 100% !important;
}

.generating-pdf tr {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

.generating-pdf td {
  padding: 10px !important;
  vertical-align: top !important;
  border: 1px solid #ddd !important;
}

.generating-pdf th {
  padding: 10px !important;
  background-color: #f2f2f2 !important;
  border: 1px solid #ddd !important;
}

/* Calendar specific styles */
.rdp {
  --rdp-cell-size: 40px;
  --rdp-accent-color: hsl(var(--primary));
  --rdp-background-color: hsl(var(--background));
  --rdp-accent-color-dark: hsl(var(--primary));
  --rdp-background-color-dark: hsl(var(--background));
  --rdp-outline: 2px solid var(--rdp-accent-color);
  --rdp-outline-selected: 2px solid rgba(0, 0, 0, 0.75);
}

.rdp-months {
  display: flex;
  flex-direction: column;
}

.rdp-month {
  margin: 0;
}

.rdp-table {
  width: 100%;
  max-width: none;
  border-collapse: separate;
  border-spacing: 2px;
}

.rdp-head_cell {
  width: calc(100% / 7);
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
  padding: 0.5rem 0;
}

.rdp-row {
  display: flex;
  width: 100%;
}

.rdp-cell {
  width: calc(100% / 7);
  text-align: center;
  position: relative;
  padding: 0;
}

.rdp-button {
  width: 100%;
  height: var(--rdp-cell-size);
  border: none;
  background: transparent;
  font-size: 0.875rem;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rdp-button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rdp-button:focus {
  outline: var(--rdp-outline);
  outline-offset: 2px;
  z-index: 1;
}

.rdp-day_today {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  font-weight: 600;
}

.rdp-day_selected {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

.rdp-day_outside {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
}

.rdp-day_disabled {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
}

.rdp-nav_button {
  width: 2rem;
  height: 2rem;
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.rdp-nav_button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rdp-nav_button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-caption {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 2.5rem;
  height: 2.5rem;
}

.rdp-caption_label {
  font-size: 1rem;
  font-weight: 600;
  color: hsl(var(--foreground));
}
