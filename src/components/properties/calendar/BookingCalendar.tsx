import React, { useState, useEffect } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ChevronDown, ChevronUp, Calendar as CalendarIcon } from 'lucide-react';
import { BookingCalendarProps } from './types';
import { useCalendarEvents } from './useCalendarEvents';
import { dateHasEvent, getEventTypesForDate, getEventsForSelectedDate } from './calendarUtils';
import CalendarFilterControls from './CalendarFilters';
import EventList from './EventList';
import CalendarLegend from './CalendarLegend';

// Local storage key for calendar expanded state
const CALENDAR_EXPANDED_KEY = 'propertyCalendarExpanded';

const BookingCalendar: React.FC<BookingCalendarProps> = ({ propertyId }) => {
  // Initialize expanded state from localStorage, default to true
  const [isExpanded, setIsExpanded] = useState(() => {
    const stored = localStorage.getItem(CALENDAR_EXPANDED_KEY);
    return stored ? JSON.parse(stored) : true;
  });
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [filters, setFilters] = useState({
    bookings: true,
    maintenance: true,
    damages: true
  });

  // Fetch calendar events
  const { events, loading } = useCalendarEvents(propertyId);

  // Save expanded state to localStorage
  useEffect(() => {
    localStorage.setItem(CALENDAR_EXPANDED_KEY, JSON.stringify(isExpanded));
  }, [isExpanded]);

  // For debugging, check the booking events for this property
  useEffect(() => {
    if (events.length > 0) {
      console.log('DEBUG: Booking events for this property:', events
        .filter(e => e.type === 'booking')
        .map(event => ({
          title: event.title,
          startDate: event.date.toLocaleDateString(),
          endDate: event.endDate ? event.endDate.toLocaleDateString() : 'N/A'
        }))
      );
    }
  }, [events]);

  return (
    <Card className="mt-6 mb-20">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5 text-primary" />
            <CardTitle>Property Calendar</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-1" />
                Hide Calendar
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-1" />
                Show Calendar
              </>
            )}
          </Button>
        </div>
        {isExpanded && (
          <CardDescription>
            View upcoming bookings, scheduled maintenance, and open damage reports
          </CardDescription>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                month={currentMonth}
                onMonthChange={setCurrentMonth}
                showOutsideDays={true}
                fixedWeeks={true}
                className="rounded-md border p-3 pointer-events-auto w-full"
                modifiers={{
                  hasBooking: (date) => {
                    const eventTypes = getEventTypesForDate(date, events, filters);
                    return eventTypes.includes('booking');
                  },
                  hasMaintenance: (date) => {
                    const eventTypes = getEventTypesForDate(date, events, filters);
                    return eventTypes.includes('maintenance');
                  },
                  hasDamage: (date) => {
                    const eventTypes = getEventTypesForDate(date, events, filters);
                    return eventTypes.includes('damage');
                  },
                  hasMultiple: (date) => {
                    const eventTypes = getEventTypesForDate(date, events, filters);
                    return eventTypes.length > 1;
                  }
                }}
                modifiersStyles={{
                  hasBooking: {
                    backgroundColor: 'rgb(147 51 234 / 0.1)',
                    color: 'rgb(147 51 234)',
                    fontWeight: 'bold',
                  },
                  hasMaintenance: {
                    backgroundColor: 'rgb(59 130 246 / 0.1)',
                    color: 'rgb(59 130 246)',
                    fontWeight: 'bold',
                  },
                  hasDamage: {
                    backgroundColor: 'rgb(245 158 11 / 0.1)',
                    color: 'rgb(245 158 11)',
                    fontWeight: 'bold',
                  },
                  hasMultiple: {
                    backgroundColor: 'rgb(239 68 68 / 0.1)',
                    color: 'rgb(239 68 68)',
                    fontWeight: 'bold',
                    border: '2px solid rgb(239 68 68)',
                  }
                }}
              />

              {/* Calendar Legend */}
              <div className="mt-4">
                <CalendarLegend />
              </div>
            </div>
            <div>
              <div className="mb-4">
                <h3 className="text-sm font-medium mb-2">
                  {selectedDate ? (
                    <>Events for {selectedDate.toLocaleDateString()}</>
                  ) : (
                    <>Select a date to view events</>
                  )}
                </h3>

                <CalendarFilterControls
                  filters={filters}
                  onFilterChange={setFilters}
                />
              </div>

              <EventList
                events={getEventsForSelectedDate(selectedDate, events, filters)}
                loading={loading}
              />
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default BookingCalendar;
