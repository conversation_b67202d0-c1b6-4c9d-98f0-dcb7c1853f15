import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { usePermissionManagement } from '@/hooks/usePermissionManagement';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/contexts/AuthContext';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { PermissionType } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { PlusCircle, RefreshCw, Database, AlertCircle } from 'lucide-react';

interface PermissionManagementProps {
  teamId: string;
}

const PermissionManagement: React.FC<PermissionManagementProps> = ({ teamId }) => {
  const { teamMembers, teams, loading: teamLoading, fetchTeamMembers } = useTeamManagement();
  const { permissions, loading: permissionsLoading, fetchUserPermissions, addUserPermission, updateUserPermission, removeUserPermission } = usePermissionManagement();
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedPermission, setSelectedPermission] = useState<PermissionType | ''>('');
  const [error, setError] = useState<string | null>(null);
  const initialLoadDoneRef = useRef(false);
  // Add local state for team members to fix the setTeamMembers error
  const [localTeamMembers, setTeamMembers] = useState<any[]>([]);

  // Get the query client outside of the function
  const queryClient = useQueryClient();

  // Cache for team members to prevent duplicate fetches
  const teamMembersCacheRef = useRef<{[key: string]: {timestamp: number, fetched: boolean}}>({});
  const TEAM_MEMBERS_CACHE_TTL = 5000; // 5 seconds cache TTL - reduced to ensure fresh data
  const isLoadingTeamMembersRef = useRef(false);

  // Function to load team members with error handling using React Query's built-in functionality
  const loadTeamMembers = useCallback(async (force = false) => {
    if (!teamId) return;

    // Create a cache key for this team
    const cacheKey = `team-${teamId}`;

    // Check if we have a valid cache entry and we're not forcing a refresh
    const now = Date.now();
    const cacheEntry = teamMembersCacheRef.current[cacheKey];
    const isCacheValid = cacheEntry && (now - cacheEntry.timestamp < TEAM_MEMBERS_CACHE_TTL) && cacheEntry.fetched;

    // If we have a valid cache entry and we're not forcing a refresh, skip the fetch
    // But only if we actually have team members loaded
    if (isCacheValid && !force && teamMembers.length > 0) {
      console.log(`PermissionManagement - Using cached team members for team: ${teamId}`);
      return;
    }

    // Prevent concurrent loads unless forced
    if (isLoadingTeamMembersRef.current && !force) {
      console.log('PermissionManagement - Already loading team members, skipping duplicate load');
      return;
    }

    // Set loading flag
    isLoadingTeamMembersRef.current = true;

    // Update cache timestamp
    teamMembersCacheRef.current[cacheKey] = {
      timestamp: now,
      fetched: false
    };

    setError(null);
    console.log('PermissionManagement - Fetching team members for team:', teamId, force ? '(forced)' : '');

    try {
      // First try a direct database query to ensure we have the latest data
      try {
        console.log('PermissionManagement - Trying direct database query first');
        const { data, error } = await supabase
          .from('team_members')
          .select(`
            *,
            profiles:user_id (id, email, first_name, last_name, avatar_url, role)
          `)
          .eq('team_id', teamId);

        if (error) {
          console.error('PermissionManagement - Error in direct database query:', error);
        } else if (data && data.length > 0) {
          console.log(`PermissionManagement - Direct query found ${data.length} team members`);

          // Mark as fetched in cache
          teamMembersCacheRef.current[cacheKey] = {
            timestamp: now,
            fetched: true
          };

          // Update the team members state directly
          setTeamMembers(data.map(member => ({
            ...member,
            ...member.profiles
          })));

          // If we got team members directly, we don't need to use the hook's fetch method
          console.log('PermissionManagement - Team members fetch completed via direct query');
          return;
        }
      } catch (dbErr) {
        console.error('PermissionManagement - Error in direct database query:', dbErr);
      }

      // Only use the hook's fetch method if direct query didn't work
      console.log('PermissionManagement - Direct query didn\'t find team members, using hook\'s fetch method');
      await fetchTeamMembers(teamId);

      // Mark as fetched in cache
      teamMembersCacheRef.current[cacheKey] = {
        timestamp: now,
        fetched: true
      };

      console.log('PermissionManagement - Team members fetch completed via hook');
    } catch (err) {
      console.error('PermissionManagement - Error fetching team members:', err);
      setError('Failed to load team members. Please try again.');

      // Mark as not fetched in cache
      teamMembersCacheRef.current[cacheKey] = {
        timestamp: 0, // Invalidate cache
        fetched: false
      };
    } finally {
      // Clear loading flag
      isLoadingTeamMembersRef.current = false;
    }
  }, [teamId, fetchTeamMembers]);

  // Track if initial load is done for each team
  const initialLoadDoneMapRef = useRef<{[key: string]: boolean}>({});
  const initialLoadAttemptedRef = useRef<{[key: string]: boolean}>({});

  // Single useEffect for loading team members to prevent multiple loads and flashing
  const teamMembersLengthRef = useRef(0);
  const teamIdChangedRef = useRef(false);

  useEffect(() => {
    // Only load if we have a teamId
    if (!teamId) return;

    // Check if teamId has changed
    if (teamIdChangedRef.current !== teamId) {
      teamIdChangedRef.current = teamId;
      console.log(`PermissionManagement - Team ID changed to ${teamId}`);
    } else if (teamMembers.length === teamMembersLengthRef.current && teamMembers.length > 0) {
      // Skip if team members length hasn't changed and we have team members
      return;
    }

    // Update the ref with current length
    teamMembersLengthRef.current = teamMembers.length;

    // Create a cache key for this team
    const cacheKey = `team-${teamId}`;

    // Always force a load if we don't have any team members
    const shouldForceLoad = teamMembers.length === 0;

    // Check if we've already loaded this team and have team members
    if (initialLoadDoneMapRef.current[cacheKey] && teamMembers.length > 0) {
      console.log(`PermissionManagement - Initial load already done for team ${teamId}, skipping`);
      return;
    }

    // Mark that we've attempted to load this team
    initialLoadAttemptedRef.current[cacheKey] = true;

    console.log(`PermissionManagement - Loading team members (force: ${shouldForceLoad})`);

    // Track if the component is still mounted
    let isMounted = true;

    const loadData = async () => {
      try {
        // Load team members with force flag if we don't have any team members
        await loadTeamMembers(shouldForceLoad);

        if (isMounted) {
          console.log(`PermissionManagement - Initial load for team: ${teamId}`);

          // If we have team members now, mark as loaded
          if (teamMembers.length > 0) {
            initialLoadDoneMapRef.current[cacheKey] = true;
          } else {
            // If we still don't have team members, try a direct query again
            console.log(`PermissionManagement - No team members found after initial load, trying direct query`);

            try {
              const { data, error } = await supabase
                .from('team_members')
                .select(`
                  *,
                  profiles:user_id (id, email, first_name, last_name, avatar_url, role)
                `)
                .eq('team_id', teamId);

              if (error) {
                console.error('PermissionManagement - Error in fallback direct query:', error);
              } else if (data && data.length > 0 && isMounted) {
                console.log(`PermissionManagement - Fallback query found ${data.length} team members`);

                // Update the team members state directly
                setTeamMembers(data.map(member => ({
                  ...member,
                  ...member.profiles
                })));

                initialLoadDoneMapRef.current[cacheKey] = true;
                teamMembersLengthRef.current = data.length;
              } else {
                console.log(`PermissionManagement - No team members found in fallback query`);
              }
            } catch (fallbackErr) {
              console.error('PermissionManagement - Error in fallback query:', fallbackErr);
            }
          }
        }
      } catch (err) {
        console.error('PermissionManagement - Error in initial team members load:', err);
        if (isMounted) {
          setError('Failed to load team members. Please try again.');
        }
      }
    };

    // Load data immediately
    loadData();

    // Set up a retry if we don't have team members after a short delay
    // But only do this once per team ID
    const retryTimer = setTimeout(() => {
      if (isMounted && teamMembers.length === 0 && !initialLoadDoneMapRef.current[cacheKey]) {
        console.log(`PermissionManagement - No team members after initial load, retrying`);
        loadTeamMembers(true);
      }
    }, 1000);

    // Cleanup function
    return () => {
      isMounted = false;
      clearTimeout(retryTimer);
    };
  }, [teamId, loadTeamMembers]);

  // Load permissions when a user is selected
  const lastSelectedUserRef = useRef<string | null>(null);
  const permissionsLoadingRef = useRef<boolean>(false);
  const permissionsLoadedRef = useRef<{[key: string]: boolean}>({});
  const lastPermissionsLengthRef = useRef<number>(0);

  useEffect(() => {
    // Only load if we have a teamId and selectedUserId
    if (!teamId || !selectedUserId) return;

    // Create a unique key for this user+team combination
    const cacheKey = `${selectedUserId}:${teamId}`;

    // Prevent duplicate loads
    if (permissionsLoadingRef.current) {
      console.log(`PermissionManagement - Already loading permissions, skipping duplicate load`);
      return;
    }

    // Only reload when the user changes or if permissions haven't been loaded yet
    const userChanged = selectedUserId !== lastSelectedUserRef.current;
    const permissionsNotLoaded = !permissionsLoadedRef.current[cacheKey];

    // Skip if permissions are already loaded and the user hasn't changed
    // Also skip if permissions.length hasn't changed since last check
    if (!userChanged && !permissionsNotLoaded && permissions.length === lastPermissionsLengthRef.current) {
      return;
    }

    // Update the last permissions length
    lastPermissionsLengthRef.current = permissions.length;

    console.log(`PermissionManagement - Loading permissions for user ${selectedUserId} in team ${teamId}`);

    // Set loading flag
    permissionsLoadingRef.current = true;

    // Track the current user we're loading for
    lastSelectedUserRef.current = selectedUserId;

    // Load permissions - always force a fresh load when user changes
    fetchUserPermissions(selectedUserId, teamId)
      .then(() => {
        console.log(`PermissionManagement - Successfully loaded permissions for user ${selectedUserId}`);
        // Mark these permissions as loaded
        permissionsLoadedRef.current[cacheKey] = true;
      })
      .catch(err => {
        console.error(`PermissionManagement - Error loading permissions:`, err);
      })
      .finally(() => {
        permissionsLoadingRef.current = false;
      });
  }, [selectedUserId, teamId, fetchUserPermissions]);

  // Set initial selected user when team members are loaded
  const initialUserSetRef = useRef(false);

  // Use both teamMembers from the hook and localTeamMembers
  const combinedTeamMembers = localTeamMembers.length > 0 ? localTeamMembers : teamMembers;

  // Force load team members when component mounts
  useEffect(() => {
    if (teamId) {
      console.log(`PermissionManagement - Initial team members load for team: ${teamId}`);
      loadTeamMembers(true);
    }
  }, [teamId, loadTeamMembers]);

  useEffect(() => {
    // Only set the initial user once when team members are first loaded
    if (combinedTeamMembers.length > 0 && !selectedUserId && !initialUserSetRef.current) {
      console.log('PermissionManagement - Setting initial selected user:', combinedTeamMembers[0].user_id);
      initialUserSetRef.current = true;
      setSelectedUserId(combinedTeamMembers[0].user_id);

      // Force load permissions for the initial user
      const initialUserId = combinedTeamMembers[0].user_id;
      if (initialUserId && teamId) {
        console.log(`PermissionManagement - Force loading initial permissions for user ${initialUserId} in team ${teamId}`);

        // Small delay to ensure state is updated
        setTimeout(() => {
          fetchUserPermissions(initialUserId, teamId)
            .then(() => {
              console.log(`PermissionManagement - Successfully loaded initial permissions for user ${initialUserId}`);
              permissionsLoadedRef.current[`${initialUserId}:${teamId}`] = true;
            })
            .catch(err => {
              console.error(`PermissionManagement - Error loading initial permissions:`, err);
            });
        }, 100);
      }
    }
  }, [combinedTeamMembers, selectedUserId, teamId, fetchUserPermissions]);

  const handleAddPermission = async () => {
    if (selectedUserId && selectedPermission) {
      await addUserPermission(selectedUserId, selectedPermission as PermissionType, teamId);
      setSelectedPermission('');
    }
  };

  const handleTogglePermission = async (permissionId: string, currentEnabled: boolean) => {
    if (selectedUserId) {
      await updateUserPermission(permissionId, !currentEnabled, selectedUserId, teamId);
    }
  };

  const handleRemovePermission = async (permissionId: string) => {
    if (selectedUserId) {
      await removeUserPermission(permissionId, selectedUserId, teamId);
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0) || '';
    const last = lastName?.charAt(0) || '';
    return (first + last).toUpperCase() || 'U';
  };

  const getPermissionLabel = (permission: PermissionType) => {
    const labels: Partial<Record<PermissionType, string>> = {
      [PermissionType.MANAGE_PROPERTIES]: 'Manage Properties',
      [PermissionType.SUBMIT_DAMAGE_REPORTS]: 'Submit Damage Reports',
      [PermissionType.MANAGE_INVENTORY]: 'Manage Inventory',
      [PermissionType.VIEW_INVENTORY]: 'View Inventory',
      [PermissionType.MANAGE_STAFF]: 'Manage Staff',
      [PermissionType.MANAGE_SERVICE_PROVIDERS]: 'Manage Service Providers',
      [PermissionType.VIEW_REPORTS]: 'View Reports',
      [PermissionType.EDIT_REPORTS]: 'Edit Reports',
      [PermissionType.VIEW_MAINTENANCE]: 'View Maintenance',
      [PermissionType.MANAGE_MAINTENANCE]: 'Manage Maintenance',
      [PermissionType.VIEW_DAMAGE_REPORTS]: 'View Damage Reports',
      [PermissionType.MANAGE_DAMAGE_REPORTS]: 'Manage Damage Reports',
      [PermissionType.VIEW_PURCHASE_ORDERS]: 'View Purchase Orders',
      [PermissionType.MANAGE_PURCHASE_ORDERS]: 'Manage Purchase Orders',
      [PermissionType.ADMIN_DASHBOARD_ACCESS]: 'Admin Dashboard Access',
      [PermissionType.IMPERSONATE_USERS]: 'Impersonate Users',
      [PermissionType.EDIT_USER_DATA]: 'Edit User Data',
      [PermissionType.ADD_USERS]: 'Add Users',
      [PermissionType.DELETE_USERS]: 'Delete Users',
      [PermissionType.MANAGE_SUBSCRIPTIONS]: 'Manage Subscriptions',
      [PermissionType.ADMIN]: 'Admin',
      [PermissionType.MANAGE_TEAM]: 'Manage Team',
      [PermissionType.VIEW_TEAM]: 'View Team'
    };
    return labels[permission] || String(permission);
  };

  // Filter out permissions that are already assigned to the user for the specific team
  const availablePermissions = Object.values(PermissionType).filter(
    permission => !permissions.some(p => p.permission === permission && p.team_id === teamId)
  ) as PermissionType[];

  const { hasPermission, isAdmin } = usePermissions();
  const { authState } = useAuth();

  // Store team ownership status in a ref to prevent re-renders
  const teamOwnershipRef = useRef<{[key: string]: boolean}>({});
  const teamOwnershipCheckedRef = useRef<{[key: string]: boolean}>({});
  const isCheckingOwnershipRef = useRef(false);

  // Check if the user is the team owner - with better error handling and debugging
  // Use a more efficient approach to prevent infinite loops
  const isTeamOwner = useMemo(() => {
    if (!teamId || !authState.user?.id) {
      console.log('PermissionManagement - Missing teamId or userId for isTeamOwner check');
      return false;
    }

    // Create a unique key for this team+user combination
    const ownershipKey = `${teamId}:${authState.user.id}`;

    // If we've already checked this team, return the cached result
    if (teamOwnershipCheckedRef.current[ownershipKey]) {
      return teamOwnershipRef.current[ownershipKey] || false;
    }

    // Check from teams array first - this is the most efficient path
    if (teams.length > 0) {
      const team = teams.find(t => t.id === teamId);
      if (team) {
        const isOwner = team.owner_id === authState.user?.id;
        console.log(`PermissionManagement - User ${authState.user?.id} is ${isOwner ? '' : 'not '}the owner of team ${teamId}`);

        // Cache the result
        teamOwnershipRef.current[ownershipKey] = isOwner;
        teamOwnershipCheckedRef.current[ownershipKey] = true;

        return isOwner;
      }
    }

    // If we don't have the team in the array or we're still loading teams,
    // check if we've already done a direct DB check
    if (teamOwnershipCheckedRef.current[ownershipKey]) {
      return teamOwnershipRef.current[ownershipKey] || false;
    }

    // Prevent multiple simultaneous checks
    if (isCheckingOwnershipRef.current) {
      return false;
    }

    // If we haven't checked yet and we're not already checking, do a direct DB check
    // but don't block the render
    console.log('PermissionManagement - Teams array is empty or team not found, checking ownership via DB');

    // Mark as checking to prevent duplicate checks
    isCheckingOwnershipRef.current = true;

    // Set a default value in the cache to prevent repeated checks
    teamOwnershipCheckedRef.current[ownershipKey] = true;
    teamOwnershipRef.current[ownershipKey] = false;

    // Schedule the check for after render with a small delay to prevent rapid re-renders
    setTimeout(() => {
      const checkOwnership = async () => {
        try {
          const { data, error } = await supabase
            .from('teams')
            .select('owner_id')
            .eq('id', teamId)
            .single();

          if (error) {
            console.error('PermissionManagement - Error fetching team:', error);
            isCheckingOwnershipRef.current = false;
            return;
          }

          const isOwner = data.owner_id === authState.user?.id;
          console.log(`PermissionManagement - Direct DB check: User is ${isOwner ? '' : 'not '}the owner`);

          // Update the cached result
          teamOwnershipRef.current[ownershipKey] = isOwner;

          // Clear the checking flag
          isCheckingOwnershipRef.current = false;
        } catch (err) {
          console.error('PermissionManagement - Error in direct DB check:', err);
          isCheckingOwnershipRef.current = false;
        }
      };

      checkOwnership();
    }, 100); // Small delay to prevent rapid re-renders

    // Return the default value for now
    return false;
  }, [teamId, teams, authState.user?.id]);

  // Allow team owners, admins, and users with manage_staff permission to manage permissions
  const canManageStaff = isAdmin() || isTeamOwner || hasPermission(PermissionType.MANAGE_STAFF, teamId);

  // Use refs to track loading state to prevent flashing
  const loadingRef = useRef({
    team: teamLoading,
    permissions: permissionsLoading
  });

  // Only update the loading state if it's been the same for a short period
  // This prevents flashing when loading states change rapidly
  useEffect(() => {
    loadingRef.current.team = teamLoading;
    loadingRef.current.permissions = permissionsLoading;
  }, [teamLoading, permissionsLoading]);

  // Compute loading state from refs
  const loading = loadingRef.current.team || loadingRef.current.permissions;

  if (loading && !combinedTeamMembers.length) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8 border rounded-lg bg-destructive/10">
        <p className="text-destructive">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => {
            setError(null);
            loadTeamMembers(true);
          }}
        >
          Try Again
        </Button>
      </div>
    );
  }

  if (!combinedTeamMembers.length) {
    return (
      <div className="text-center p-8 border rounded-lg bg-muted/10">
        <p className="text-muted-foreground">No team members found to manage permissions.</p>
        <p className="text-sm text-muted-foreground mt-2">
          Try refreshing the page or clicking the button below.
        </p>
        <div className="flex flex-col sm:flex-row gap-2 justify-center mt-4">
          <Button
            variant="outline"
            onClick={() => loadTeamMembers(true)}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={async () => {
              console.log('Manual direct query for team members');
              try {
                const { data, error } = await supabase
                  .from('team_members')
                  .select(`
                    *,
                    profiles:user_id (id, email, first_name, last_name, avatar_url, role)
                  `)
                  .eq('team_id', teamId);

                if (error) {
                  console.error('Error in manual direct query:', error);
                  setError('Failed to load team members: ' + error.message);
                } else if (data && data.length > 0) {
                  console.log(`Manual query found ${data.length} team members`);

                  // Update the team members state directly
                  setTeamMembers(data.map(member => ({
                    ...member,
                    ...member.profiles
                  })));

                  toast({
                    title: "Success",
                    description: `Loaded ${data.length} team members`,
                  });
                } else {
                  console.log('No team members found in manual query');
                  toast({
                    title: "No team members found",
                    description: "This team doesn't have any members yet.",
                    variant: "destructive"
                  });
                }
              } catch (err) {
                console.error('Error in manual query:', err);
                setError('Failed to load team members. Please try again.');
              }
            }}
          >
            <Database className="h-4 w-4 mr-2" />
            Direct Query
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center gap-4">
        <div className="flex-1">
          <label className="text-sm font-medium">Select Team Member</label>
          <Select
            value={selectedUserId || ''}
            onValueChange={(value) => setSelectedUserId(value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select a team member" />
            </SelectTrigger>
            <SelectContent>
              {combinedTeamMembers.map((member) => (
                <SelectItem key={member.user_id} value={member.user_id}>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={member.avatar_url || undefined} />
                      <AvatarFallback>
                        {getInitials(member.first_name, member.last_name)}
                      </AvatarFallback>
                    </Avatar>
                    <span>
                      {member.first_name || member.last_name
                        ? `${member.first_name || ''} ${member.last_name || ''}`
                        : member.email || 'Unknown User'}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex-1">
          <label className="text-sm font-medium">Add Permission</label>
          <div className="flex mt-1 gap-2">
            <Select
              value={selectedPermission}
              onValueChange={(value) => setSelectedPermission(value as PermissionType)}
              disabled={availablePermissions.length === 0 || !canManageStaff}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select permission" />
              </SelectTrigger>
              <SelectContent>
                {availablePermissions.length === 0 ? (
                  <SelectItem value="none" disabled>
                    No available permissions
                  </SelectItem>
                ) : (
                  availablePermissions.map((permission) => (
                    <SelectItem key={permission} value={permission}>
                      {getPermissionLabel(permission)}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            <Button
              onClick={handleAddPermission}
              disabled={!selectedPermission || loading || !canManageStaff}
              className="flex-shrink-0"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>
        </div>
      </div>

      {selectedUserId && (
        <div>
          <h3 className="text-lg font-medium mb-4">Current Permissions</h3>

          {/* Use a stable rendering approach to prevent flashing */}
          <div className="min-h-[200px] relative">
            {/* Always render the permissions grid if we have permissions */}
            {permissions.length > 0 && (
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 relative">
                {permissions.map((permission) => (
                  <Card key={permission.id}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center justify-between">
                        {getPermissionLabel(permission.permission)}
                        <Switch
                          checked={permission.enabled}
                          onCheckedChange={() => handleTogglePermission(permission.id, permission.enabled)}
                          disabled={!canManageStaff || permissionsLoadingRef.current}
                        />
                      </CardTitle>
                      <CardDescription className="text-xs">
                        {permission.enabled ? 'Enabled' : 'Disabled'}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full text-destructive"
                        onClick={() => handleRemovePermission(permission.id)}
                        disabled={!canManageStaff || permissionsLoadingRef.current}
                      >
                        Remove
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Show loading spinner when loading and no permissions */}
            {loading && permissions.length === 0 && (
              <div className="absolute inset-0 flex justify-center items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}

            {/* Show loading overlay when loading with permissions */}
            {permissionsLoadingRef.current && permissions.length > 0 && (
              <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-10 rounded-lg">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}

            {/* Show no permissions message when not loading and no permissions */}
            {!loading && permissions.length === 0 && (
              <div className="absolute inset-0 flex justify-center items-center">
                <div className="text-center p-4 border rounded-lg bg-muted/10 w-full">
                  <p className="text-muted-foreground">No permissions assigned.</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PermissionManagement;
